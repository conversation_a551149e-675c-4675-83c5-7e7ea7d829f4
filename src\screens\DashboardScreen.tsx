import React from 'react';
import { View, StyleSheet } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import useTheme from '../theme/useTheme';

export default function DashboardScreen() {
    const theme = useTheme();

    return (
        <ThemedView useSafedArea style={styles.container}>
            <View style={styles.content}>
                <ThemedText variant="title" style={styles.title}>
                    Dashboard
                </ThemedText>
                <ThemedText variant="body" style={[styles.subtitle, { color: theme.text + '80' }]}>
                    Coming soon...
                </ThemedText>
            </View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
    },
    content: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        marginBottom: 10,
    },
    subtitle: {
        textAlign: 'center',
    },
});
