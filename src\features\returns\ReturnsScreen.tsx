import React from 'react';
import { View, StyleSheet } from 'react-native';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';

export default function ReturnsScreen() {
    const theme = useTheme();

    return (
        <View style={styles.container}>
            <View style={[styles.comingSoonContainer, { backgroundColor: theme.cardBackground }]}>
                <ThemedText variant="title" style={styles.comingSoonText}>
                    Coming Soon
                </ThemedText>
                <ThemedText style={[styles.description, { color: theme.text + '80' }]}>
                    Returns functionality will be available in a future update.
                </ThemedText>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
    },
    comingSoonContainer: {
        padding: 40,
        borderRadius: 16,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    comingSoonText: {
        fontSize: 28,
        marginBottom: 16,
    },
    description: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
    },
});
