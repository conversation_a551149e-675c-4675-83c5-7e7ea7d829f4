// utils/authStorage.ts
import * as SecureStore from 'expo-secure-store';

export const saveAuthData = async (token: string, user: any) => {
    await SecureStore.setItemAsync('authToken', token);
    await SecureStore.setItemAsync('authUser', JSON.stringify(user));
};

export const getAuthData = async () => {
    const token = await SecureStore.getItemAsync('authToken');
    const userJson = await SecureStore.getItemAsync('authUser');
    const user = userJson ? JSON.parse(userJson) : null;
    return { token, user };
};

export const clearAuthData = async () => {
    await SecureStore.deleteItemAsync('authToken');
    await SecureStore.deleteItemAsync('authUser');
};
