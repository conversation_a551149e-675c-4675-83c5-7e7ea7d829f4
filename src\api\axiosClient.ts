// utils/axiosClient.ts
import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { store } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';

const axiosClient = axios.create({
    baseURL: 'http://192.168.1.78:8000/api/v1',
    headers: {
        'Content-Type': 'application/json',
    },
});

// Inject token before requests
axiosClient.interceptors.request.use(async (config) => {
    const token = await SecureStore.getItemAsync('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle 401 responses
axiosClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        if (error.response?.status === 401) {
            await clearAuthData();
            store.dispatch(logout());

        }
        return Promise.reject(error);
    }
);

export default axiosClient;
