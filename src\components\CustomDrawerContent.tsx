import React from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { MaterialIcons, FontAwesome6 } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';
import { clearSelectedWarehouse } from '../features/warehouses/warehouseSlice';

export default function CustomDrawerContent(props: DrawerContentComponentProps) {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { user } = useSelector((state: RootState) => state.auth);
    const { collections, loading: collectionsLoading } = useSelector((state: RootState) => state.collections);
    const { navigation, state } = props;

    const handleLogout = async () => {
        dispatch(logout());
        dispatch(clearSelectedWarehouse());
        await clearAuthData();
    };

    const navigateToScreen = (screenName: string) => {
        navigation.navigate(screenName);
    };

    const isActiveRoute = (routeName: string) => {
        return state.routeNames[state.index] === routeName;
    };

    const renderMenuItem = (
        screenName: string,
        IconComponent: any,
        iconName: string,
        label: string,
        rightElement?: React.ReactNode
    ) => {
        const active = isActiveRoute(screenName);
        return (
            <TouchableOpacity
                style={styles.menuItemWrapper}
                onPress={() => navigateToScreen(screenName)}
            >
                {active && (
                    <LinearGradient
                        colors={[theme.primary, 'transparent']}
                        start={{ x: 0, y: 0.5 }}
                        end={{ x: 1, y: 0.5 }}
                        style={StyleSheet.absoluteFillObject}
                    />
                )}
                <View
                    style={[
                        styles.menuItem,
                        active && { backgroundColor: theme.primary + '10' }
                    ]}
                >
                    <IconComponent
                        name={iconName}
                        size={24}
                        color={active ? 'white' : theme.text + '80'}
                    />
                    <View style={styles.menuItemContent}>
                        <ThemedText style={[
                            styles.menuText,
                            { color: active ? 'white' : theme.text }
                        ]}>
                            {label}
                        </ThemedText>
                        {rightElement}
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
                <View style={styles.userInfo}>
                    <View style={[styles.avatar, { backgroundColor: theme.background }]}>
                        <FontAwesome6 name="user" size={15} color={theme.text} />
                    </View>
                    <View style={styles.userDetails}>
                        <ThemedText variant="subtitle" style={styles.userName}>
                            {user?.name || 'User Name'}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.userEmail}>
                            {user?.email || '<EMAIL>'}
                        </ThemedText>
                    </View>
                </View>
            </View>

            {/* Menu Items */}
            <DrawerContentScrollView {...props} style={styles.scrollView}>
                <View style={styles.menuItems}>
                    {renderMenuItem('Dashboard', MaterialIcons, 'dashboard', 'Dashboard')}
                    {renderMenuItem(
                        'Collections',
                        FontAwesome6,
                        'cubes-stacked',
                        'Collects',
                        <View style={[styles.countChip, { backgroundColor: theme.error }]}>
                            {collectionsLoading ? (
                                <ActivityIndicator size="small" color={theme.background} />
                            ) : (
                                <ThemedText variant="body" style={[styles.countText, { color: theme.background }]}>
                                    {collections.length}
                                </ThemedText>
                            )}
                        </View>
                    )}
                    {renderMenuItem('Returns', MaterialIcons, 'assignment-return', 'Returns')}
                </View>
            </DrawerContentScrollView>

            {/* Bottom Section */}
            <View style={[styles.bottomSection, { borderTopColor: theme.text + '20' }]}>
                <TouchableOpacity style={styles.bottomItem}>
                    <MaterialIcons name="settings" size={24} color={theme.text + '80'} />
                    <ThemedText style={[styles.menuText, { color: theme.text }]}>
                        Settings
                    </ThemedText>
                </TouchableOpacity>

                <TouchableOpacity style={styles.bottomItem} onPress={handleLogout}>
                    <MaterialIcons name="logout" size={24} color="red" />
                    <ThemedText style={[styles.menuText, { color: 'red' }]}>
                        Logout
                    </ThemedText>
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingTop: 30,
    },
    header: {
        padding: 20,
        paddingTop: 30,
        borderRadius: 10,
        margin: 10,
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: 30,
        height: 30,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 15,
    },
    userDetails: {
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    userName: {
        fontWeight: '900',
        marginBottom: 4,
    },
    userEmail: {
        fontSize: 14,
    },
    scrollView: {
        flex: 1,
    },
    menuItems: {
        paddingTop: 20,
    },
    menuItemWrapper: {
        position: 'relative',
        marginHorizontal: 5,
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 10,
        height: 50,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderRadius: 8,
        zIndex: 2,
    },
    menuText: {
        fontSize: 16,
        fontWeight: '500',
    },
    menuItemContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginLeft: 15,
    },
    countChip: {
        borderRadius: 999,
        minHeight: 23,
        minWidth: 23,
        paddingHorizontal: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    countText: {
        fontSize: 12,
        fontWeight: '600',
    },
    gradientBackground: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        maxWidth: 80,
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 20,
        marginTop: 16,
        borderTopLeftRadius: 8,
        borderBottomLeftRadius: 8,
        zIndex: 1,
    },
    bottomSection: {
        borderTopWidth: 1,
        paddingTop: 10,
        paddingBottom: 20,
    },
    bottomItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
    },
});
