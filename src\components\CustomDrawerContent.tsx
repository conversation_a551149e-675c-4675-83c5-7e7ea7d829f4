import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { MaterialIcons, FontAwesome6 } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';
import { clearSelectedWarehouse } from '../features/warehouses/warehouseSlice';

export default function CustomDrawerContent(props: DrawerContentComponentProps) {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { user } = useSelector((state: RootState) => state.auth);
    const { navigation, state } = props;

    const handleLogout = async () => {
        dispatch(logout());
        dispatch(clearSelectedWarehouse());
        await clearAuthData();
    };

    const navigateToScreen = (screenName: string) => {
        navigation.navigate(screenName);
    };

    const isActiveRoute = (routeName: string) => {
        return state.routeNames[state.index] === routeName;
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            {/* Header with user info */}
            <View style={[styles.header, { backgroundColor: theme.primary }]}>
                <View style={styles.userInfo}>
                    <View style={[styles.avatar, { backgroundColor: theme.background + '20' }]}>
                        <FontAwesome6 name="user" size={24} color={theme.background} />
                    </View>
                    <View style={styles.userDetails}>
                        <ThemedText variant="subtitle" style={[styles.userName, { color: theme.background }]}>
                            {user?.name || 'User Name'}
                        </ThemedText>
                        <ThemedText variant="body" style={[styles.userEmail, { color: theme.background + '80' }]}>
                            {user?.email || '<EMAIL>'}
                        </ThemedText>
                    </View>
                </View>
            </View>

            {/* Menu Items */}
            <DrawerContentScrollView {...props} style={styles.scrollView}>
                <View style={styles.menuItems}>
                    {/* Dashboard */}
                    <TouchableOpacity
                        style={[
                            styles.menuItem,
                            isActiveRoute('Dashboard') && { backgroundColor: theme.primary + '10' }
                        ]}
                        onPress={() => navigateToScreen('Dashboard')}
                    >
                        <MaterialIcons 
                            name="dashboard" 
                            size={24} 
                            color={isActiveRoute('Dashboard') ? theme.primary : theme.text + '80'} 
                        />
                        <ThemedText style={[
                            styles.menuText,
                            { color: isActiveRoute('Dashboard') ? theme.primary : theme.text }
                        ]}>
                            Dashboard
                        </ThemedText>
                    </TouchableOpacity>

                    {/* Process */}
                    <TouchableOpacity
                        style={[
                            styles.menuItem,
                            isActiveRoute('Process') && { backgroundColor: theme.primary + '10' }
                        ]}
                        onPress={() => navigateToScreen('Process')}
                    >
                        <FontAwesome6 
                            name="cubes-stacked" 
                            size={20} 
                            color={isActiveRoute('Process') ? theme.primary : theme.text + '80'} 
                        />
                        <ThemedText style={[
                            styles.menuText,
                            { color: isActiveRoute('Process') ? theme.primary : theme.text }
                        ]}>
                            Process
                        </ThemedText>
                    </TouchableOpacity>
                </View>
            </DrawerContentScrollView>

            {/* Bottom section */}
            <View style={[styles.bottomSection, { borderTopColor: theme.text + '20' }]}>
                {/* Settings */}
                <TouchableOpacity style={styles.bottomItem}>
                    <MaterialIcons name="settings" size={24} color={theme.text + '80'} />
                    <ThemedText style={[styles.menuText, { color: theme.text }]}>
                        Settings
                    </ThemedText>
                </TouchableOpacity>

                {/* Logout */}
                <TouchableOpacity style={styles.bottomItem} onPress={handleLogout}>
                    <MaterialIcons name="logout" size={24} color="red" />
                    <ThemedText style={[styles.menuText, { color: 'red' }]}>
                        Logout
                    </ThemedText>
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        padding: 20,
        paddingTop: 50,
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: 50,
        height: 50,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 15,
    },
    userDetails: {
        flex: 1,
    },
    userName: {
        fontWeight: '600',
        marginBottom: 4,
    },
    userEmail: {
        fontSize: 14,
    },
    scrollView: {
        flex: 1,
    },
    menuItems: {
        paddingTop: 20,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        marginHorizontal: 10,
        borderRadius: 8,
    },
    menuText: {
        marginLeft: 15,
        fontSize: 16,
        fontWeight: '500',
    },
    bottomSection: {
        borderTopWidth: 1,
        paddingTop: 10,
        paddingBottom: 20,
    },
    bottomItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
    },
});
