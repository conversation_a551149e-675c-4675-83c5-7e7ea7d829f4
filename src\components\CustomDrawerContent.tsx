import React from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { MaterialIcons, FontAwesome6 } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';
import { clearSelectedWarehouse } from '../features/warehouses/warehouseSlice';

export default function CustomDrawerContent(props: DrawerContentComponentProps) {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { user } = useSelector((state: RootState) => state.auth);
    const { collections, loading: collectionsLoading } = useSelector((state: RootState) => state.collections);
    const { navigation, state } = props;

    const handleLogout = async () => {
        dispatch(logout());
        dispatch(clearSelectedWarehouse());
        await clearAuthData();
    };

    const navigateToScreen = (screenName: string) => {
        navigation.navigate(screenName);
    };

    const isActiveRoute = (routeName: string) => {
        return state.routeNames[state.index] === routeName;
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            {/* Header with user info */}
            <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
                <View style={styles.userInfo}>
                    <View style={[styles.avatar, { backgroundColor: theme.background }]}>
                        <FontAwesome6 name="user" size={15} color={theme.text} />
                    </View>
                    <View style={styles.userDetails}>
                        <ThemedText variant="subtitle" style={styles.userName}>
                            {user?.name || 'User Name'}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.userEmail}>
                            {user?.email || '<EMAIL>'}
                        </ThemedText>
                    </View>
                </View>
            </View>

            {/* Menu Items */}
            <DrawerContentScrollView {...props} style={styles.scrollView}>
                <View style={styles.menuItems}>
                    {/* Dashboard */}
                    <TouchableOpacity
                        style={[
                            styles.menuItem,
                            isActiveRoute('Dashboard') && { backgroundColor: theme.primary + '10' }
                        ]}
                        onPress={() => navigateToScreen('Dashboard')}
                    >
                        <MaterialIcons
                            name="dashboard"
                            size={24}
                            color={isActiveRoute('Dashboard') ? theme.primary : theme.text + '80'}
                        />
                        <ThemedText style={[
                            styles.menuText,
                            { color: isActiveRoute('Dashboard') ? theme.primary : theme.text }
                        ]}>
                            Dashboard
                        </ThemedText>
                    </TouchableOpacity>

                    {/* Collections */}
                    <TouchableOpacity
                        style={[
                            styles.menuItem,
                            isActiveRoute('Collections') && { backgroundColor: theme.primary + '10' }
                        ]}
                        onPress={() => navigateToScreen('Collections')}
                    >
                        <FontAwesome6
                            name="cubes-stacked"
                            size={20}
                            color={isActiveRoute('Collections') ? theme.primary : theme.text + '80'}
                        />
                        <View style={styles.menuItemContent}>
                            <ThemedText style={[
                                styles.menuText,
                                { color: isActiveRoute('Collections') ? theme.primary : theme.text }
                            ]}>
                                Collections
                            </ThemedText>
                            {/* Collections count chip */}
                            <View style={[styles.countChip, { backgroundColor: theme.primary }]}>
                                {collectionsLoading ? (
                                    <ActivityIndicator size="small" color={theme.background} />
                                ) : (
                                    <ThemedText variant="body" style={[styles.countText, { color: theme.background }]}>
                                        {collections.length}
                                    </ThemedText>
                                )}
                            </View>
                        </View>
                    </TouchableOpacity>

                    {/* Returns */}
                    <TouchableOpacity
                        style={[
                            styles.menuItem,
                            isActiveRoute('Returns') && { backgroundColor: theme.primary + '10' }
                        ]}
                        onPress={() => navigateToScreen('Returns')}
                    >
                        <MaterialIcons
                            name="assignment-return"
                            size={24}
                            color={isActiveRoute('Returns') ? theme.primary : theme.text + '80'}
                        />
                        <ThemedText style={[
                            styles.menuText,
                            { color: isActiveRoute('Returns') ? theme.primary : theme.text }
                        ]}>
                            Returns
                        </ThemedText>
                    </TouchableOpacity>
                </View>
            </DrawerContentScrollView>

            {/* Bottom section */}
            <View style={[styles.bottomSection, { borderTopColor: theme.text + '20' }]}>
                {/* Settings */}
                <TouchableOpacity style={styles.bottomItem}>
                    <MaterialIcons name="settings" size={24} color={theme.text + '80'} />
                    <ThemedText style={[styles.menuText, { color: theme.text }]}>
                        Settings
                    </ThemedText>
                </TouchableOpacity>

                {/* Logout */}
                <TouchableOpacity style={styles.bottomItem} onPress={handleLogout}>
                    <MaterialIcons name="logout" size={24} color="red" />
                    <ThemedText style={[styles.menuText, { color: 'red' }]}>
                        Logout
                    </ThemedText>
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingTop: 30,
    },
    header: {
        padding: 20,
        paddingTop: 30,
        borderRadius: 10,
        margin: 10,
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: 30,
        height: 30,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 15,
    },
    userDetails: {
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    userName: {
        fontWeight: '900',
        marginBottom: 4,
    },
    userEmail: {
        fontSize: 14,
    },
    scrollView: {
        flex: 1,
    },
    menuItems: {
        paddingTop: 20,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        marginHorizontal: 5,
        borderRadius: 8,
    },
    menuText: {
        marginLeft: 15,
        fontSize: 16,
        fontWeight: '500',
    },
    menuItemContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginLeft: 15,
    },
    countChip: {
        borderRadius: 999,
        minHeight: 20,
        minWidth: 20,
        paddingHorizontal: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    countText: {
        fontSize: 12,
        fontWeight: '600',
    },
    bottomSection: {
        borderTopWidth: 1,
        paddingTop: 10,
        paddingBottom: 20,
    },
    bottomItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
    },
});
