import { OrderProduct } from "../navigation/type";

export const getCollectionStatusFromProducts = (allProducts: OrderProduct[]): 'created' | 'processing' | 'completed' => {
    const total = allProducts.length;
    const gatheredCount = allProducts.filter(p => p.status === 'gathered').length;

    if (gatheredCount === 0) return 'created';
    if (gatheredCount === total) return 'completed';
    return 'processing';
};
