import React from 'react';
import { StyleSheet, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import StatusRenderer, { StatusType } from './StatusRenderer';

type Props = {
    collection: {
        status: StatusType;
        num: string;
        orders: any[];
    };
};

export default function CollectionDetailsCard({ collection }: Props) {
    const theme = useTheme();

    return (
        <View style={[styles.card, { backgroundColor: theme.cardBackground }]}>
            <View style={styles.row}>
                <ThemedText variant="subtitle">Status</ThemedText>
                <StatusRenderer status={collection.status} />
            </View>

            <View style={styles.row}>
                <ThemedText variant="subtitle">Reference</ThemedText>
                <ThemedText variant="subtitle">#{collection.num}</ThemedText>
            </View>

            <View style={styles.row}>
                <ThemedText variant="subtitle">Orders</ThemedText>
                <ThemedText variant="subtitle">{collection.orders ?? 0}</ThemedText>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    card: {
        width: '100%',
        padding: 16,
        borderRadius: 12,
        marginVertical: 12,
        elevation: 2, // Android shadow
        shadowColor: '#000', // iOS shadow
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 15,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',

        height: 30,
    },
});
