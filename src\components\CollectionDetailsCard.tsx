import React from 'react';
import { StyleSheet, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import StatusRenderer, { StatusType } from './StatusRenderer';
import { Collection } from '../navigation/type';
import ShippingRender from './ShippingRender';
import CollectionCardSkeleton from './CollectionCardSkeleton';

type Props = {
    collection: Collection;
    loading: boolean;
};



export default function CollectionDetailsCard({ collection, loading }: Props) {
    const theme = useTheme();

    return (
        loading ? <CollectionCardSkeleton /> :
            <View style={[styles.card, { backgroundColor: theme.cardBackground }]}>
                <View style={styles.row}>
                    <ThemedText variant="subtitle">Status</ThemedText>
                    <StatusRenderer status={collection.status} />
                </View>
                <View style={styles.row}>
                    <ThemedText variant="subtitle">Orders</ThemedText>
                    <ThemedText variant="subtitle">{collection.orders ?? 0}</ThemedText>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 5 }}>
                    <ShippingRender status={collection.shippingType} />
                    <ThemedText variant="subtitle"> {collection.shippingBy}</ThemedText>
                </View>


            </View>
    );
}

const styles = StyleSheet.create({
    card: {
        width: '100%',
        padding: 16,
        borderRadius: 12,
        marginVertical: 12,
        elevation: 2, // Android shadow
        shadowColor: '#000', // iOS shadow
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 15,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',

        height: 30,
    },
});
