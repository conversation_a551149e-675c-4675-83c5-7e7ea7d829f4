export type OrderProduct = {
    id: number;
    name: string;
    quantity: number;
    sku: string;
    status: string;
};
export type Collection = {
    id: number;
    num: string;
    location: string;
    status: string;
    shippingType: string;
    shippingBy: string;
    orders: number;
    addedAt: string;
}
export type Order = {
    id: number;
    orderNum: string;
    orderCode: string;
    status: string;
    goodsDescription: string;
    ordersProducts: OrderProduct[];
    [key: string]: any; // allow extra fields
};

export type RootStackParamList = {
    Splash: undefined;
    Login: undefined;
    MainApp: undefined;
};

export type DrawerParamList = {
    Dashboard: undefined;
    Collections: undefined;
    Returns: undefined;
};

export type MainTabParamList = {
    Collections: undefined;
    Returns: undefined;
};

export type CollectionStackParamList = {
    CollectionList: undefined;
    CollectionDetails: { collection: any };
    Fulfillment: { collectionId: string; orders: Order[] };
};