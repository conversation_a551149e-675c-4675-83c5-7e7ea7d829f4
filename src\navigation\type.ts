export type OrderProduct = {
    id: number;
    name: string;
    quantity: number;
    status: string;
};

export type Order = {
    id: number;
    orderNum: string;
    orderCode: string;
    status: string;
    sku: string;
    goodsDescription: string;
    ordersProducts: OrderProduct[];
    [key: string]: any; // allow extra fields
};

export type RootStackParamList = {
    Splash: undefined;
    Login: undefined;
    Warehouses: undefined;
    MainApp: undefined;
};

export type MainTabParamList = {
    Collections: undefined;
    Returns: undefined;
};

export type CollectionStackParamList = {
    CollectionList: undefined;
    CollectionDetails: { collection: any };
    Fulfillment: { collectionId: string; orders: Order[] };
};