import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { Order } from '../navigation/type';

type Props = {
    order: Order;
    onPress: () => void;
};

export default function OrderGridItem({ order, onPress }: Props) {
    const theme = useTheme();

    return (
        <TouchableOpacity onPress={onPress} style={[styles.container, { backgroundColor: theme.cardBackground }]}>
            <View style={styles.left}>
                <ThemedText variant="body" style={{ fontWeight: 'bold' }}>#{order.orderNum}</ThemedText>
                <View style={{ display: 'flex', flexDirection: 'row', gap: 10, justifyContent: 'flex-start', alignItems: 'flex-start', paddingTop: 5, opacity: 0.5 }}>
                    <View style={{ width: 1, backgroundColor: theme.text, height: '100%' }} />
                    <View style={{ display: 'flex', flexDirection: 'column', gap: 10, justifyContent: 'flex-start', alignItems: 'flex-start', paddingTop: 5 }}>
                        {order.ordersProducts.map((line, index) => (

                            <ThemedText key={index} variant="body" style={[styles.productsRows, { fontWeight: line.status === 'gathered' ? 'bold' : 'normal' }]} numberOfLines={1} >
                                {line.status === 'gathered' && <ThemedText variant="body" style={{ color: theme.success, fontWeight: 'bold' }}>✓</ThemedText>} {line.quantity} x {line.name}
                            </ThemedText>


                        ))}
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    container: {
        padding: 10,
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        elevation: 1,
    },
    left: {
        flex: 1,
        marginRight: 12,
    },
    productsRows: {
        fontSize: 12,
        fontWeight: 'normal'
    },
});
