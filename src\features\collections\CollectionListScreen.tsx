import React, { useCallback, useEffect, useRef } from 'react';
import { View, FlatList, Dimensions } from 'react-native';
import ThemedText from '../../components/ThemedText';
import CollectionCard from '../../components/CollectionCard';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Swipeable } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { AntDesign } from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../app/store';
import { fetchCollectionsByWarehouseId } from './CollectionsThunks';
import PageTitleSkeleton from '../../components/PageTitleSkeleton';
import { CollectionStackParamList } from '../../navigation/type';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import CollectionCardSkeleton from '../../components/CollectionCardSkeleton';
import ThemedView from '../../components/ThemedView';

export default function CollectionListScreen() {
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NativeStackNavigationProp<CollectionStackParamList>>();
    const { collections, loading } = useSelector((state: RootState) => state.collections);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);

    // const [collections, setCollections] = useState([
    //     { id: '1', name: 'Kreepta Collection', num: '25467', createdAt: '2022-01-01', status: 'created', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 4 },
    //     { id: '2', name: 'Summer Fashion', num: '25468', createdAt: '2022-02-15', status: 'shipped', shippingType: 'pickup', shippedAt: '2022-02-20', shippedBy: 'John Doe', orders: 12 },
    //     { id: '3', name: 'Winter Essentials', num: '25469', createdAt: '2022-03-10', status: 'processing', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 8 },
    //     { id: '4', name: 'Sports Equipment', num: '25470', createdAt: '2022-04-05', status: 'processing', shippingType: 'delivery', shippedAt: '2022-04-10', shippedBy: 'Jane Smith', orders: 15 },
    //     { id: '5', name: 'Electronics Bundle', num: '25471', createdAt: '2022-05-20', status: 'processing', shippingType: 'pickup', shippedAt: null, shippedBy: null, orders: 0 },
    //     { id: '6', name: 'Home Decor', num: '25472', createdAt: '2022-06-15', status: 'created', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 6 },
    //     { id: '7', name: 'Kitchen Essentials', num: '25473', createdAt: '2022-07-01', status: 'shipped', shippingType: 'pickup', shippedAt: null, shippedBy: null, orders: 9 },
    //     { id: '8', name: 'Garden Tools', num: '25474', createdAt: '2022-08-10', status: 'shipped', shippingType: 'delivery', shippedAt: '2022-08-15', shippedBy: 'Mike Johnson', orders: 7 },
    // ]);

    useEffect(() => {
        if (selectedWarehouse) {
            dispatch(fetchCollectionsByWarehouseId(selectedWarehouse.id));
        }
    }, [dispatch, selectedWarehouse?.id]);

    const onRefresh = useCallback(() => {
        if (selectedWarehouse) {
            dispatch(fetchCollectionsByWarehouseId(selectedWarehouse.id));
        }
    }, [dispatch, selectedWarehouse]);



    function SwipeableCollectionRow({ item, onPress }: { item: any; onPress: () => void }) {
        const swipeRef = useRef<Swipeable>(null);

        const printCollection = () => {
            console.log('🖨️ Printing:', item.name);
            swipeRef.current?.close();
        };



        const screenWidth = Dimensions.get('window').width;

        const renderRightActions = (collection: any, dragX: any) => {
            return (
                <LinearGradient
                    colors={['transparent', '#007AFF']}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                    style={{
                        width: screenWidth,
                        maxWidth: 80,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        paddingRight: 20,
                        borderRadius: 8,
                        marginVertical: 2,
                    }}
                >
                    <AntDesign name="printer" size={24} color="#fff" />
                </LinearGradient>
            );
        };

        return (
            <Swipeable
                ref={swipeRef}
                renderRightActions={(progress, dragX) => renderRightActions(item, dragX)}
                onSwipeableOpen={printCollection}
            >
                {
                    loading ? <CollectionCardSkeleton /> :
                        <CollectionCard collection={item} onPress={onPress} />}
            </Swipeable>
        );
    }

    return (
        <ThemedView>
            <View style={{ height: 20, display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', }} >
                {/* {!loading ? (
                    <ThemedText style={{ fontSize: 24, textAlign: 'center' }} variant="title">
                        {collections.length} Collections
                    </ThemedText>
                ) : (
                    <ThreeDotsSkeleton />
                )} */}
            </View>


            <FlatList
                contentContainerStyle={{ paddingHorizontal: 15 }}
                data={collections}
                keyExtractor={(item) => item.id.toFixed(0)}
                ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
                renderItem={({ item }) => (
                    <SwipeableCollectionRow
                        item={item}
                        onPress={() => navigation.navigate('CollectionDetails', { collection: item })} />
                )}
                refreshing={loading}
                onRefresh={onRefresh}
            />
        </ThemedView>
    );
}
