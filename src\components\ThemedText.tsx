import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import useTheme from '../theme/useTheme';

interface ThemedTextProps extends TextProps {
    variant?: 'title' | 'subtitle' | 'body';
}

export default function ThemedText({ variant = 'body', style, ...props }: ThemedTextProps) {
    const theme = useTheme();

    const variantStyle = {
        title: styles.title,
        subtitle: styles.subtitle,
        body: styles.body,
    }[variant];

    return (
        <Text style={[{ color: theme.text }, variantStyle, style]} {...props} />
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
    },
    body: {
        fontSize: 14,
    },
});
