import React from 'react';
import { GestureResponderEvent, StyleSheet, TouchableOpacity, View, Dimensions } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { FontAwesome6 } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing } from 'react-native-reanimated';

type Props = {
    warehouse: any;
    onPress: (event: GestureResponderEvent) => void;
    isSelected?: boolean;
    loading?: boolean;
};

const SkeletonBox = ({ width, height, borderRadius = 8 }: { width: number, height: number, borderRadius?: number }) => {
    const opacity = useSharedValue(0.3);

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));

    React.useEffect(() => {
        opacity.value = withRepeat(
            withTiming(1, {
                duration: 800,
                easing: Easing.inOut(Easing.ease),
            }),
            -1,
            true
        );
    }, []);

    return (
        <Animated.View
            style={[
                {
                    backgroundColor: '#ccc',
                    borderRadius,
                    width,         // only number now
                    height,
                },
                animatedStyle,
            ]}
        />
    );
};


export default function WareHouseCard({ warehouse, onPress, isSelected, loading }: Props) {
    const theme = useTheme();

    const handlePress = (event: any) => {
        if (!loading && onPress) {
            onPress(event);
        }
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            disabled={loading}
            style={[
                styles.button,
                {
                    backgroundColor: isSelected ? theme.primary + '20' : theme.cardBackground,
                    borderColor: theme.primary,
                    borderWidth: isSelected ? 2 : 0,
                },
            ]}
        >
            <View style={styles.content}>
                <View style={styles.left}>
                    {loading ? (
                        <>
                            <SkeletonBox width={120} height={16} />
                            <SkeletonBox width={80} height={14} />
                        </>
                    ) : (
                        <>
                            <ThemedText variant="title">{warehouse.name}</ThemedText>
                            <ThemedText variant="body">{warehouse.location}</ThemedText>
                        </>
                    )}
                </View>
                <View style={styles.right}>
                    {loading ? (
                        <>
                            <SkeletonBox width={30} height={30} borderRadius={15} />
                            <SkeletonBox width={20} height={14} />
                        </>
                    ) : (
                        <>
                            <FontAwesome6 name="cubes-stacked" size={30} color={theme.text} />
                            <ThemedText variant="body">{warehouse.collections}</ThemedText>
                        </>
                    )}
                </View>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        alignItems: 'center',
    },
    content: {
        flex: 1,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    left: {
        flexDirection: 'column',
        gap: 6,
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    right: {
        flexDirection: 'column',
        gap: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
