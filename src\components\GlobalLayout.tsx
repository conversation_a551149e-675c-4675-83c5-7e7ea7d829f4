import React, { useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { DrawerActions } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import WarehouseDropdown from './WarehouseDropdown';
import useTheme from '../theme/useTheme';
import { useThemeMode } from '../theme/ThemeContext';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';

interface CollectionsLayoutProps {
    children: React.ReactNode;
}

export default function GlobalLayout({ children }: CollectionsLayoutProps) {
    const theme = useTheme();
    const { resolvedTheme } = useThemeMode();
    const navigation = useNavigation();



    const openDrawer = () => {
        navigation.dispatch(DrawerActions.openDrawer());
    };

    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
            <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} />

            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.background, borderBottomColor: theme.text + '20' }]}>
                <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
                    <MaterialIcons name="menu" size={24} color={theme.text} />
                </TouchableOpacity>

                <WarehouseDropdown />
            </View>

            {/* Content */}
            <View style={[styles.content, { backgroundColor: theme.background }]}>
                {children}
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
    },
    header: {
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        borderBottomWidth: 1,
    },
    menuButton: {
        padding: 5,
    },
    content: {
        flex: 1,
    },
});
