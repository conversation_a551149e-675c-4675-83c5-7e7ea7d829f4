// src/features/collections/collectionsThunks.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';

export const fetchCollectionsByWarehouseId = createAsyncThunk(
    'collections/fetchByWarehouseId',
    async (warehouseId: number, { rejectWithValue }) => {

        try {
            const response = await axiosClient.get(`/warehouse/${warehouseId}/collections`);
            return response.data; // Adjust depending on API structure
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching collections');
        }
    }
);

//GETCOLLECTIONbY ID 
export const fetchCollectionById = createAsyncThunk(
    'collections/fetchById',
    async (collectionId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/collections/${collectionId}`);
            return response.data;
        } catch (err: any) {
            console.log(err.response?.data);

            return rejectWithValue(err.response?.data || 'Error fetching collection');
        }
    }
);
