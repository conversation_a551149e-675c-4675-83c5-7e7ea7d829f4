import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withRepeat,
    Easing,
} from 'react-native-reanimated';
import useTheme from '../theme/useTheme';

const { width } = Dimensions.get('window');

const SkeletonBox = ({ width: boxWidth, height, style }: { width: number; height: number; style?: any }) => {
    const theme = useTheme();
    const opacity = useSharedValue(0.3);

    useEffect(() => {
        opacity.value = withRepeat(
            withTiming(0.8, {
                duration: 1000,
                easing: Easing.inOut(Easing.ease),
            }),
            -1,
            true
        );
    }, []);

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));

    return (
        <Animated.View
            style={[
                {
                    width: boxWidth,
                    height,
                    backgroundColor: theme.text,
                    borderRadius: 8,
                },
                animatedStyle,
                style,
            ]}
        />
    );
};

export default function FulfillmentSkeleton() {
    const theme = useTheme();

    return (
        <View style={styles.container}>
            {/* Header Skeleton */}
            <View style={styles.header}>
                <SkeletonBox width={30} height={30} />
                <SkeletonBox width={150} height={24} />
            </View>

            {/* Progress Line Skeleton */}
            <View style={styles.progressContainer}>
                <SkeletonBox width={width - 32} height={4} />
                <SkeletonBox width={100} height={12} style={{ marginTop: 8 }} />
            </View>

            {/* Order Details Card Skeleton */}
            <View style={styles.orderCard}>
                <View style={styles.orderHeader}>
                    <View style={styles.orderInfo}>
                        <SkeletonBox width={120} height={18} />
                        <SkeletonBox width={100} height={14} style={{ marginTop: 4 }} />
                        <SkeletonBox width={80} height={14} style={{ marginTop: 2 }} />
                    </View>
                    <SkeletonBox width={44} height={44} style={{ borderRadius: 22 }} />
                </View>
                <SkeletonBox width={width - 64} height={40} style={{ marginTop: 12 }} />
            </View>

            {/* Products List Skeleton */}
            <View style={styles.productsContainer}>
                <SkeletonBox width={120} height={16} />
                
                {/* Product Items */}
                {[1, 2, 3, 4].map((item) => (
                    <View key={item} style={styles.productRow}>
                        <SkeletonBox width={30} height={16} />
                        <SkeletonBox width={width * 0.5} height={16} />
                        <SkeletonBox width={24} height={24} />
                    </View>
                ))}
            </View>

            {/* Footer Skeleton */}
            <View style={styles.footer}>
                <SkeletonBox width={60} height={40} />
                <SkeletonBox width={80} height={16} />
                <SkeletonBox width={60} height={40} />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f4f4f4',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        gap: 16,
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        alignItems: 'center',
    },
    orderCard: {
        backgroundColor: '#f8f9fa',
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 16,
        marginVertical: 16,
    },
    orderHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    orderInfo: {
        flex: 1,
    },
    productsContainer: {
        flex: 1,
        paddingHorizontal: 16,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8,
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
});
