import React, { useEffect } from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import { loginSuccess } from '../features/auth/authSlice';
import { getAuthData } from '../utils/authStorage';
import { RootState } from '../app/store';
import { RootStackParamList } from '../navigation/type'; // adjust path if needed

export default function SplashScreen() {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'Splash'>>();
    const dispatch = useDispatch();
    const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

    useEffect(() => {
        (async () => {
            const { token, user } = await getAuthData();
            if (token && user) {
                dispatch(loginSuccess({ token, user }));
            }
        })();
    }, []);

    useEffect(() => {
        if (isLoggedIn) {
            navigation.replace('Warehouses'); // now properly typed
        } else {
            navigation.replace('Login');
        }
    }, [isLoggedIn]);

    return (
        <View style={styles.container}>
            <ActivityIndicator size="large" />
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1, justifyContent: 'center', alignItems: 'center' },
});
