import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import { loginSuccess } from '../features/auth/authSlice';
import { getAuthData } from '../utils/authStorage';
import { RootState, AppDispatch } from '../app/store';
import { RootStackParamList } from '../navigation/type';
import { fetchWarehouses, selectWarehouse } from '../features/warehouses/warehouseSlice';
import { fetchCollectionsByWarehouseId } from '../features/collections/CollectionsThunks';

export default function SplashScreen() {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'Splash'>>();
    const dispatch = useDispatch<AppDispatch>();
    const { isLoggedIn } = useSelector((state: RootState) => state.auth);
    const { warehouses, loading: warehousesLoading } = useSelector((state: RootState) => state.warehouses);
    const [authChecked, setAuthChecked] = useState(false);

    // Check authentication on mount
    useEffect(() => {
        const checkAuth = async () => {
            try {
                const { token, user } = await getAuthData();
                if (token && user) {
                    dispatch(loginSuccess({ token, user }));
                    // Fetch warehouses immediately after successful auth
                    dispatch(fetchWarehouses());
                }
            } catch (error) {
                console.error('Auth check failed:', error);
            } finally {
                setAuthChecked(true);
            }
        };

        checkAuth();
    }, [dispatch]);

    // Handle navigation after auth check and warehouse loading
    useEffect(() => {
        if (!authChecked) return; // Wait for auth check to complete

        if (!isLoggedIn) {
            navigation.replace('Login');
            return;
        }

        // If logged in and warehouses are loaded, auto-select first warehouse and navigate
        if (warehouses.length > 0 && !warehousesLoading) {
            const firstWarehouse = warehouses[0];
            dispatch(selectWarehouse(firstWarehouse));
            dispatch(fetchCollectionsByWarehouseId(firstWarehouse.id));
            navigation.replace('MainApp');
        } else if (authChecked && isLoggedIn && !warehousesLoading && warehouses.length === 0) {
            // If no warehouses available, still navigate to MainApp (it will handle the empty state)
            navigation.replace('MainApp');
        }
    }, [authChecked, isLoggedIn, warehouses, warehousesLoading, navigation, dispatch]);

    return (
        <View style={styles.container}>
            <ActivityIndicator size="large" />
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1, justifyContent: 'center', alignItems: 'center' },
});
