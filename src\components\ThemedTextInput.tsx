import React, { useState } from 'react';
import {
    TextInput,
    StyleSheet,
    TextInputProps,
    View,
    TouchableOpacity,
} from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedIcon from './ThemedIcon';

interface ThemedTextInputProps extends TextInputProps {
    isPassword?: boolean;
}

export default function ThemedTextInput({ isPassword = false, style, ...props }: ThemedTextInputProps) {
    const theme = useTheme();
    const [secure, setSecure] = useState(isPassword);

    const toggleSecure = () => {
        setSecure(!secure);
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.inputBackground }]}>
            <TextInput
                placeholderTextColor={theme.text}
                secureTextEntry={secure}
                style={[
                    styles.input,
                    { color: theme.text },
                    style,
                    isPassword && { paddingRight: 40 }, // room for eye icon
                ]}
                {...props}
            />
            {isPassword && (
                <TouchableOpacity onPress={toggleSecure} style={styles.icon}>
                    <ThemedIcon name={secure ? 'eye-off' : 'eye'} size={20} />
                </TouchableOpacity>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        marginBottom: 16,
        position: 'relative',
        justifyContent: 'center',
    },
    input: {
        padding: 12,
        fontSize: 16,
    },
    icon: {
        position: 'absolute',
        right: 12,
        top: '50%',
        marginTop: -10,
    },
});
