import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, TouchableOpacity, ScrollView, RefreshControl, Alert } from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Feather } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    interpolate,
    runOnJS
} from 'react-native-reanimated';
import * as Print from 'expo-print';
import { shareAsync } from 'expo-sharing';

import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import CollectionDetailsCard from '../../components/CollectionDetailsCard';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders } from '../orders/orderSlice';
import { useDispatch, useSelector } from 'react-redux';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import { Order, OrderProduct, CollectionStackParamList } from '../../navigation/type';

type CollectionDetailsRouteProp = RouteProp<CollectionStackParamList, 'CollectionDetails'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'CollectionDetails'>;

export default function CollectionDetailsScreen() {
    const { params: { collection } } = useRoute<CollectionDetailsRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const theme = useTheme();
    const { loading: ordersLoading, orders } = useSelector((state: RootState) => state.orders);
    const [refreshing, setRefreshing] = useState(false);
    const [buttonsPosition, setButtonsPosition] = useState(0);
    const scrollViewRef = useRef<ScrollView>(null);
    const buttonsRef = useRef<View>(null);

    const scrollY = useSharedValue(0);
    const stickyHeaderOpacity = useSharedValue(0);
    const stickyHeaderTranslateY = useSharedValue(-60);

    useFocusEffect(
        useCallback(() => {
            dispatch(fetchOrders(collection.id));
        }, [dispatch, collection.id])
    );

    const onRefresh = async () => {
        setRefreshing(true);
        await dispatch(fetchOrders(collection.id));
        setRefreshing(false);
    };

    const updateStickyHeader = (shouldShow: boolean) => {
        'worklet';
        stickyHeaderOpacity.value = withTiming(shouldShow ? 1 : 0, { duration: 200 });
        stickyHeaderTranslateY.value = withTiming(shouldShow ? 0 : -60, { duration: 200 });
    };

    const handleScroll = (event: any) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        scrollY.value = currentScrollY;
        const threshold = buttonsPosition - 50;
        const shouldShow = currentScrollY >= threshold;
        runOnJS(updateStickyHeader)(shouldShow);
    };

    const handleButtonsLayout = (event: any) => {
        const { y } = event.nativeEvent.layout;
        setButtonsPosition(y);
    };

    const generatePDF = async () => {
        try {
            const htmlContent = `
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .collection-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                        .collection-info { font-size: 14px; color: #666; margin-bottom: 20px; }
                        .sku-group { margin-bottom: 25px; }
                        .sku-header { font-size: 18px; font-weight: bold; margin-bottom: 10px; border-bottom: 2px solid #333; padding-bottom: 5px; }
                        .product-row { display: flex; padding: 8px 0; border-bottom: 1px solid #eee; }
                        .qty-col { width: 20%; font-weight: bold; }
                        .name-col { width: 40%; }
                        .order-col { width: 40%; }
                        .table-header { font-weight: bold; background-color: #f5f5f5; padding: 10px 0; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="collection-title">Collection ${collection.num}</div>
                        <div class="collection-info">
                            Status: ${collection.status} | Orders: ${collection.orders} |
                            Shipping: ${collection.shippingType} by ${collection.shippingBy}
                        </div>
                    </div>

                    <div class="table-header product-row">
                        <div class="qty-col">Quantity</div>
                        <div class="name-col">Product Name</div>
                        <div class="order-col">Order Number</div>
                    </div>

                    ${Object.entries(groupedProducts).map(([sku, { totalQty, items }]) => `
                        <div class="sku-group">
                            <div class="sku-header">${totalQty} x ${sku}</div>
                            ${items.map(({ product, orderNum }) => `
                                <div class="product-row">
                                    <div class="qty-col">${product.quantity}</div>
                                    <div class="name-col">${product.name}</div>
                                    <div class="order-col">${orderNum}</div>
                                </div>
                            `).join('')}
                        </div>
                    `).join('')}
                </body>
                </html>
            `;

            const { uri } = await Print.printToFileAsync({ html: htmlContent });
            await shareAsync(uri, { mimeType: 'application/pdf' });
        } catch (error) {
            console.error('Error generating PDF:', error);
            Alert.alert('Error', 'Failed to generate PDF. Please try again.');
        }
    };

    const stickyHeaderAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: stickyHeaderOpacity.value,
            transform: [{ translateY: stickyHeaderTranslateY.value }],
        };
    });

    const allProducts = (orders || []).flatMap(order =>
        order.ordersProducts.map(product => ({ ...product, orderNum: order.orderNum }))
    );

    const groupedProducts: { [sku: string]: { name: string; totalQty: number; items: { product: OrderProduct; orderNum: string }[] } } = {};

    allProducts.forEach(({ sku, name, quantity, ...rest }) => {
        if (!groupedProducts[sku]) {
            groupedProducts[sku] = { name, totalQty: 0, items: [] };
        }
        groupedProducts[sku].totalQty += quantity;
        groupedProducts[sku].items.push({ product: { sku, name, quantity, ...rest }, orderNum: rest.orderNum });
    });

    return (
        <ThemedView>
            <Animated.View style={[
                {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    zIndex: 1000,
                    backgroundColor: theme.background,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.text + '20',
                    paddingHorizontal: 20,
                    paddingVertical: 8,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    gap: 12
                },
                stickyHeaderAnimatedStyle
            ]}>
                <TouchableOpacity
                    onPress={generatePDF}
                    style={{
                        backgroundColor: theme.success,
                        padding: 8,
                        borderRadius: 8,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                >
                    <Feather name="printer" size={24} color="white" />
                </TouchableOpacity>
                <ThemedText variant="title">{collection.num}</ThemedText>
                {ordersLoading ? <ThreeDotsSkeleton /> : (
                    <TouchableOpacity
                        onPress={() => navigation.navigate('Fulfillment', { collectionId: collection.id, orders })}
                        style={{
                            backgroundColor: theme.primary,
                            padding: 8,
                            borderRadius: 8,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <AntDesign name="arrowright" size={24} color="white" />
                    </TouchableOpacity>
                )}
            </Animated.View>

            <ScrollView
                ref={scrollViewRef}
                onScroll={handleScroll}
                scrollEventThrottle={16}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.primary} />
                }
            >
                <View style={{ flex: 1, padding: 20 }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
                        <TouchableOpacity onPress={() => navigation.goBack()} style={{ height: 34 }}>
                            <AntDesign name="arrowleft" size={30} color={theme.text} />
                        </TouchableOpacity>
                        <ThemedText style={{ fontSize: 24 }} variant="title">{collection.num}</ThemedText>
                    </View>

                    <CollectionDetailsCard collection={collection} />

                    {!ordersLoading && <View
                        ref={buttonsRef}
                        onLayout={handleButtonsLayout}
                        style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 12, gap: 12 }}
                    >
                        <TouchableOpacity
                            onPress={generatePDF}
                            style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.success, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                        >
                            <Feather name="printer" size={24} color="white" />
                            <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>Print Details</ThemedText>
                        </TouchableOpacity>

                        {ordersLoading ? <ThreeDotsSkeleton /> : (
                            <TouchableOpacity
                                onPress={() => navigation.navigate('Fulfillment', { collectionId: collection.id, orders })}
                                style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.primary, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                            >
                                <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>
                                    Start Fulfillment
                                </ThemedText>
                                <AntDesign name="arrowright" size={18} color="white" />
                            </TouchableOpacity>
                        )}
                    </View>}

                    {ordersLoading ? <ThreeDotsSkeleton /> : Object.entries(groupedProducts).map(([sku, { totalQty, items }]) => (
                        <View key={sku} style={{ marginBottom: 16 }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                                <ThemedText variant="body" style={{ fontWeight: 'bold' }}>{totalQty} x {sku}</ThemedText>
                                <View style={{ height: 2, flex: 1, backgroundColor: theme.text }} />
                            </View>
                            <View style={{ marginTop: 10, gap: 8 }}>
                                {items.map(({ product, orderNum }, idx) => (
                                    <View key={idx} style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
                                        <ThemedText style={{ width: '20%' }}>{product.quantity}</ThemedText>
                                        <ThemedText style={{ width: '40%' }}>{product.name}</ThemedText>
                                        <ThemedText style={{ width: '40%' }}>{orderNum}</ThemedText>
                                    </View>
                                ))}
                            </View>
                        </View>
                    ))}
                </View>
            </ScrollView>
        </ThemedView>
    );
}