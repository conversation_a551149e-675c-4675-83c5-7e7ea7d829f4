import React, { useEffect, useState, useCallback } from 'react';
import { View, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Feather } from '@expo/vector-icons';

import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import CollectionDetailsCard from '../../components/CollectionDetailsCard';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders } from '../orders/orderSlice';
import { useDispatch, useSelector } from 'react-redux';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import { Order, OrderProduct, CollectionStackParamList } from '../../navigation/type';

type CollectionDetailsRouteProp = RouteProp<CollectionStackParamList, 'CollectionDetails'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'CollectionDetails'>;

export default function CollectionDetailsScreen() {
    const { params: { collection } } = useRoute<CollectionDetailsRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const theme = useTheme();
    const { loading: ordersLoading, orders } = useSelector((state: RootState) => state.orders);
    const [refreshing, setRefreshing] = useState(false);
    const [expandedSku, setExpandedSku] = useState<string | null>(null);

    useFocusEffect(
        useCallback(() => {
            dispatch(fetchOrders(collection.id));
        }, [dispatch, collection.id])
    );

    const onRefresh = async () => {
        setRefreshing(true);
        await dispatch(fetchOrders(collection.id));
        setRefreshing(false);
    };

    // Flatten all products from all orders
    const allProducts = (orders || []).flatMap(order =>
        order.ordersProducts.map(product => ({ ...product, orderNum: order.orderNum }))
    );

    // Group by SKU and sum quantity
    const groupedProducts: { [sku: string]: { name: string; totalQty: number; items: { product: OrderProduct; orderNum: string }[] } } = {};


    allProducts.forEach(({ sku, name, quantity, ...rest }) => {
        if (!groupedProducts[sku]) {
            groupedProducts[sku] = { name, totalQty: 0, items: [] };
        }
        groupedProducts[sku].totalQty += quantity;
        groupedProducts[sku].items.push({ product: { sku, name, quantity, ...rest }, orderNum: rest.orderNum });
    });

    return (
        <ThemedView>
            <ScrollView
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.primary} />
                }
            >
                <View style={{ flex: 1, padding: 20 }}>
                    {/* Header */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
                        <TouchableOpacity onPress={() => navigation.goBack()} style={{ height: 34 }}>
                            <AntDesign name="arrowleft" size={30} color={theme.text} />
                        </TouchableOpacity>
                        <ThemedText style={{ fontSize: 24 }} variant="title">{collection.name}</ThemedText>
                    </View>

                    {/* Info Card */}
                    <CollectionDetailsCard collection={collection} />

                    {/* Fulfillment Buttons */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 12, gap: 12 }}>
                        <TouchableOpacity
                            style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.success, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                        >
                            <Feather name="printer" size={24} color="white" />
                            <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>Print Details</ThemedText>
                        </TouchableOpacity>

                        {ordersLoading ? <ThreeDotsSkeleton /> : (
                            <TouchableOpacity
                                onPress={() => navigation.navigate('Fulfillment', { collectionId: collection.id, orders })}
                                style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.primary, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                            >
                                <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>
                                    Start Fulfillment
                                </ThemedText>
                                <AntDesign name="arrowright" size={18} color="white" />
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Product Groups by SKU */}
                    {ordersLoading ? <ThreeDotsSkeleton /> : Object.entries(groupedProducts).map(([sku, { name, totalQty, items }]) => {


                        const isExpanded = expandedSku === sku;
                        return (
                            <View key={sku} style={{ marginBottom: 16 }}>
                                <View
                                    style={{ flexDirection: 'row', alignItems: 'center', gap: 8, }}
                                >
                                    <ThemedText variant="body" style={{ fontWeight: 'bold' }}>{totalQty} x {sku}</ThemedText>
                                    <View style={{ height: 2, flex: 1, backgroundColor: theme.text }} />
                                </View>

                                {true && (
                                    <View style={{ marginTop: 10, gap: 8 }}>
                                        {items.map(({ product, orderNum }, idx) => (
                                            <View key={idx} style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
                                                <ThemedText style={{ width: '20%' }}>{product.quantity}</ThemedText>
                                                <ThemedText style={{ width: '40%' }}>{product.name}</ThemedText>
                                                <ThemedText style={{ width: '40%' }}>{orderNum}</ThemedText>
                                            </View>
                                        ))}
                                    </View>
                                )}
                            </View>
                        );
                    })}
                </View>
            </ScrollView>
        </ThemedView>
    );
}
