import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withDelay,
    Easing,
    withRepeat
} from 'react-native-reanimated';

const Dot = ({ delay }: { delay: number }) => {
    const opacity = useSharedValue(0.3);

    opacity.value = withRepeat(
        withDelay(
            delay,
            withTiming(1, {
                duration: 300,
                easing: Easing.inOut(Easing.ease),
            })
        ),
        -1,
        true
    );

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: opacity.value,
        transform: [{ scale: opacity.value }],
    }));

    return <Animated.View style={[styles.dot, animatedStyle]} />;
};

export default function ThreeDotsSkeleton() {
    return (
        <View style={styles.container}>
            <Dot delay={0} />
            <Dot delay={150} />
            <Dot delay={300} />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        gap: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    dot: {
        width: 10,
        height: 10,
        borderRadius: 5,
        backgroundColor: '#ccc',
    },
});
