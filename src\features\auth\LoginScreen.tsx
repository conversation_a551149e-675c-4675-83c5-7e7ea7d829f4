import React, { useState, useRef } from 'react';
import { StyleSheet, View, Alert, KeyboardAvoidingView, Platform, ScrollView, TextInput } from 'react-native';
import { useDispatch } from 'react-redux';
import { loginSuccess } from './authSlice';
import axiosClient from '../../api/axiosClient';
import ThemedView from '../../components/ThemedView';
import ThemedTextInput from '../../components/ThemedTextInput';
import ThemedButton from '../../components/ThemedButton';
import ThemedText from '../../components/ThemedText';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import useTheme from '../../theme/useTheme';
import { saveAuthData } from '../../utils/authStorage';

export default function LoginScreen() {
    const dispatch = useDispatch();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const theme = useTheme();

    // Refs for input navigation
    const passwordRef = useRef<TextInput>(null);


    const handleLogin = async () => {


        try {
            setError(null);
            setLoading(true);
            const response = await axiosClient.post('/login', { email, password });
            console.log('dddd', response.data);
            if (response.data.result === 'error') {
                setError(response.data.message);
                return;
            }

            const { token, user } = response.data;

            // Store token and user securely
            await saveAuthData(token, user);
            // Update Redux
            dispatch(loginSuccess({ token, user }));
        } catch (error: any) {
            setError(error.response?.data?.message || 'Login failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <ThemedView useSafedArea style={styles.container}>
            <KeyboardAvoidingView
                style={styles.keyboardAvoidingView}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
            >
                <ScrollView
                    contentContainerStyle={styles.scrollContainer}
                    keyboardShouldPersistTaps="handled"
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.header}>
                        <FontAwesome6 name="cubes-stacked" size={80} color={theme.text} />
                        <ThemedText variant="title" style={styles.title}>Welcome back!</ThemedText>
                        <ThemedText variant="subtitle">Please log in to access your collections</ThemedText>
                    </View>

                    <View style={styles.formContainer}>
                        {error && (
                            <ThemedText variant="body" style={styles.errorText}>
                                {error}
                            </ThemedText>
                        )}

                        <ThemedTextInput
                            placeholder="Email"
                            value={email}
                            onChangeText={setEmail}
                            keyboardType="email-address"
                            autoCapitalize="none"
                            returnKeyType="next"
                            onSubmitEditing={() => passwordRef.current?.focus()}
                            blurOnSubmit={false}
                        />

                        <ThemedTextInput
                            ref={passwordRef}
                            placeholder="Password"
                            value={password}
                            onChangeText={setPassword}
                            isPassword
                            returnKeyType="go"
                            onSubmitEditing={handleLogin}
                        />

                        <View style={styles.loginButton}>
                            <ThemedButton
                                title={loading ? 'Logging in...' : 'Login'}
                                onPress={handleLogin}
                                disabled={loading}
                            />
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    keyboardAvoidingView: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
        padding: 20,
        paddingBottom: 40,
    },
    header: {
        marginVertical: 20,
        alignItems: 'center',
        flex: 0.4,
        justifyContent: 'center',
    },
    title: {
        marginTop: 8,
    },
    formContainer: {
        flex: 0.6,
        justifyContent: 'center',
        paddingTop: 20,
    },
    errorText: {
        color: 'red',
        textAlign: 'center',
        marginBottom: 16,
    },
    loginButton: {
        marginTop: 20,
    },
});
