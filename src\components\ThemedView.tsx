import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StyleSheet,
    View,
    ViewProps,
    Platform,
    StatusBar as RNStatusBar,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import useTheme from '../theme/useTheme';
import { useThemeMode } from '../theme/ThemeContext';

type Props = {
    scrollable?: boolean;
    useSafedArea?: boolean;
    children: React.ReactNode;
} & ViewProps;

export default function ThemedView({ scrollable = false, useSafedArea = false, children, style, ...rest }: Props) {
    const theme = useTheme();
    const { resolvedTheme } = useThemeMode();
    const Container = scrollable ? ScrollView : View;

    return (
        <SafeAreaView style={[styles.safeArea, { paddingTop: useSafedArea ? Platform.OS === 'android' ? RNStatusBar.currentHeight || 0 : 0 : 0, backgroundColor: theme.background }]}>
            <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} />
            <Container style={[styles.container, { backgroundColor: theme.background }, style]} {...rest}>
                {children}
            </Container>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,

    },
    container: {
        flex: 1,
    },
});
