import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { store } from '../../app/store';

// Define the type of each warehouse
export interface Warehouse {
    id: number;
    name: string;
    location: string;
    status: string;
    collections: number;
}

// Define the shape of the slice state
interface WarehouseState {
    warehouses: Warehouse[];
    selectedWarehouse: Warehouse | null;
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: WarehouseState = {
    warehouses: [],
    selectedWarehouse: null,
    loading: false,
    error: null,
};

// Async thunk to fetch warehouses
export const fetchWarehouses = createAsyncThunk(
    'warehouses/fetchWarehouses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get('/warehouses');

            //make select the first warehouse has collections , else just the first one 
            if (response.data.length > 0) {
                const firstWarehouseWithCollections = response.data.find((warehouse: Warehouse) => warehouse.collections > 0);
                if (firstWarehouseWithCollections) {
                    store.dispatch(selectWarehouse(firstWarehouseWithCollections));
                } else {
                    store.dispatch(selectWarehouse(response.data[0]));
                }
            }

            return response.data; // should be an array of warehouses

        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch warehouses');
        }
    }
);



// Slice
const warehouseSlice = createSlice({
    name: 'warehouses',
    initialState,
    reducers: {
        selectWarehouse: (state, action: PayloadAction<Warehouse>) => {
            state.selectedWarehouse = action.payload;
        },
        clearSelectedWarehouse: (state) => {
            state.selectedWarehouse = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchWarehouses.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchWarehouses.fulfilled, (state, action: PayloadAction<Warehouse[]>) => {
                state.loading = false;
                state.warehouses = action.payload;
            })
            .addCase(fetchWarehouses.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            });
    },
});

// Export actions
export const { selectWarehouse, clearSelectedWarehouse } = warehouseSlice.actions;

// Export the reducer
export default warehouseSlice.reducer;
