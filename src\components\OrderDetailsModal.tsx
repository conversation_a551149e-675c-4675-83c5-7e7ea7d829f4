import React, { forwardRef, useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions, LayoutChangeEvent } from 'react-native';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    interpolate,
    withTiming,
} from 'react-native-reanimated';
import { Modalize } from 'react-native-modalize';
import { AntDesign } from '@expo/vector-icons';
import ThemedView from './ThemedView';
import ThemedText from './ThemedText';
import { Order } from '../navigation/type';
import useTheme from '../theme/useTheme';
import { ScrollView } from 'react-native-gesture-handler';
import { extractedProducts } from '../faker/fakeData';

const { height: screenHeight } = Dimensions.get('window');

type Props = {
    order: Order | null;
    onClose?: () => void;
};

export const OrderDetailsModal = forwardRef<Modalize, Props>(({ order }, ref) => {
    const theme = useTheme();

    const fullHeight = screenHeight * 0.8;
    const snapHeight = screenHeight * 0.12;
    const progress = useSharedValue(0); // 0 = snapped, 1 = fully expanded

    const [position, setPosition] = useState<'initial' | 'top'>('initial');
    const [modalContentHeight, setModalContentHeight] = useState(0);

    const onContentLayout = (event: LayoutChangeEvent) => {
        const { height } = event.nativeEvent.layout;
        setModalContentHeight(height);
    };

    useEffect(() => {
        if (position === 'top') {
            progress.value = withTiming(1);
        } else {
            progress.value = withTiming(0);
        }
    }, [position, modalContentHeight]);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            opacity: interpolate(progress.value, [0, 1], [0, 1]),
            transform: [
                {
                    translateY: interpolate(progress.value, [0, 1], [20, 0]),
                },
            ],
        };
    });

    return (
        <Modalize
            ref={ref}
            modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
            modalHeight={fullHeight}
            snapPoint={snapHeight}
            adjustToContentHeight={false}
            handleStyle={{ backgroundColor: theme.primary, width: 100 }}
            panGestureComponentEnabled
            withHandle
            onPositionChange={(pos) => setPosition(pos)}
            onClosed={() => {
                progress.value = 0;
                setPosition('initial');
            }}
        >
            <ScrollView >
                <View onLayout={onContentLayout}>
                    <View style={styles.header}>
                        <ThemedText variant="title">Order #{order?.orderNum}</ThemedText>
                        {position === 'initial' && (
                            <AntDesign name="up" size={20} style={{ marginLeft: 'auto' }} />
                        )}
                    </View>

                    <Animated.View style={animatedStyle}>
                        {extractedProducts
                            .filter((item) => item.orderId === order?.id)
                            .map((item, index) => (
                                <ThemedText key={`${item.sku}-${index}`} style={styles.item}>
                                    {item.name} - Qty: {item.quantity}
                                </ThemedText>
                            ))}
                    </Animated.View>

                </View>
            </ScrollView>
        </Modalize>
    );
});

const styles = StyleSheet.create({
    modal: {
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        padding: 20,
        minHeight: '80%',
    },
    header: {
        flexDirection: 'row',
        gap: 12,
        alignItems: 'center',
        marginBottom: 20,
    },
    item: {
        fontSize: 16,
        marginVertical: 6,
    },
});
