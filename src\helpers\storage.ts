// helpers/storage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

type Progress = {
    currentSkuIndex: number;
    checkedItems: { [orderId: number]: { [barcode: string]: boolean } };
    gatheredSkus: {
        [sku: string]: {
            gathered: boolean;
            gatheredAt: string | null;
        };
    };
};

export async function saveProgress(collectionId: string, data: Progress) {
    try {
        await AsyncStorage.setItem(`progress_${collectionId}`, JSON.stringify(data));
    } catch (err) {
        console.error('Error saving progress:', err);
    }
}

export async function loadProgress(collectionId: string): Promise<Progress | null> {
    try {
        const data = await AsyncStorage.getItem(`progress_${collectionId}`);
        return data ? JSON.parse(data) : null;
    } catch (err) {
        console.error('Error loading progress:', err);
        return null;
    }
}
