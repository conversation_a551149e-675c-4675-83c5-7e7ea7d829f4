import React, { useEffect, useState } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Dimensions,
    ActivityIndicator,
    RefreshControl,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons, AntDesign, MaterialIcons } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withSpring,
    Easing,
} from 'react-native-reanimated';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import ThemedButton from '../../components/ThemedButton';
import useTheme from '../../theme/useTheme';
import { loadProgress } from '../../helpers/storage';
import { Order, OrderProduct } from '../../navigation/type';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders, toggleProductStatus } from '../orders/orderSlice';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';



import { CollectionStackParamList } from '../../navigation/type';

type FulfillmentRouteProp = RouteProp<CollectionStackParamList, 'Fulfillment'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'Fulfillment'>;



export default function FulfillmentScreen() {
    const { params } = useRoute<FulfillmentRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const { collectionId } = params;
    const dispatch = useDispatch<AppDispatch>();

    // Get orders from Redux state
    const { orders: reduxOrders, loading, error } = useSelector((state: RootState) => state.orders);

    // Local state for orders to avoid refetching on every toggle
    const [localOrders, setLocalOrders] = useState<Order[]>([]);
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [refreshing, setRefreshing] = useState(false);

    // Animation values
    const slideAnimation = useSharedValue(0);

    // Helper function to check if an order has products that need gathering
    const orderHasUngatheredProducts = (order: Order): boolean => {
        return order.ordersProducts.some(product => product.status !== 'gathered');
    };

    // Process all orders - include ALL orders and sort by order number
    const processedOrders = localOrders
        .map((order: Order) => ({
            ...order,
            ordersProducts: Array.isArray(order.ordersProducts) ? order.ordersProducts.map((product: OrderProduct) => ({
                ...product,
                status: product.status || 'pending'
            })) : []
        }))
        .sort((a, b) => a.orderNum.localeCompare(b.orderNum)); // Sort by order number

    const currentOrder = processedOrders[currentOrderIndex];





    // Fetch orders on component mount
    useEffect(() => {
        dispatch(fetchOrders(collectionId));
    }, [dispatch, collectionId]);

    // Sync Redux orders with local state
    useEffect(() => {
        if (reduxOrders.length > 0) {
            setLocalOrders(reduxOrders);
        }
    }, [reduxOrders]);

    // Auto-skip to first order that needs gathering on entry
    useEffect(() => {
        if (processedOrders.length > 0) {
            // Find the first order that has products still not gathered
            const firstUngatheredOrderIndex = processedOrders.findIndex(order =>
                orderHasUngatheredProducts(order)
            );

            // If found, start with that order; otherwise start with first order
            setCurrentOrderIndex(firstUngatheredOrderIndex !== -1 ? firstUngatheredOrderIndex : 0);
        }
    }, [processedOrders.length]);


    // Loading state for individual products
    const [toggleLoading, setToggleLoading] = useState<{ [productId: number]: boolean }>({});

    // Refresh function for pull-to-refresh
    const onRefresh = async () => {
        setRefreshing(true);
        try {
            await dispatch(fetchOrders(collectionId));
        } catch (error) {
            console.error('Error refreshing orders:', error);
        } finally {
            setRefreshing(false);
        }
    };

    const handleToggleCheckbox = async (productId: number) => {
        setToggleLoading((prev) => ({ ...prev, [productId]: true }));

        try {
            const res = await dispatch(toggleProductStatus({ productId }));
            if (toggleProductStatus.fulfilled.match(res)) {
                // Update local state instead of refetching all orders
                setLocalOrders(prevOrders =>
                    prevOrders.map(order => ({
                        ...order,
                        ordersProducts: order.ordersProducts.map(product =>
                            product.id === productId
                                ? { ...product, status: res.payload }
                                : product
                        )
                    }))
                );

            }
        } catch (error) {
            console.error('Error toggling product status:', error);
        } finally {
            setToggleLoading((prev) => ({ ...prev, [productId]: false }));
        }
    };



    const handlePrintLabel = () => {
        // TODO: Implement print functionality
        console.log('Print label for order:', currentOrder?.orderNum);
    };

    const handleNextOrder = () => {
        // Check if current order has all products gathered
        const currentOrderCompleted = !orderHasUngatheredProducts(currentOrder);

        if (currentOrderCompleted) {
            // Current order is completed, can move to next order
            const nextOrderIndex = currentOrderIndex + 1;
            console.log('Next order index:', nextOrderIndex, 'Total orders:', processedOrders.length);

            if (nextOrderIndex < processedOrders.length) {
                setCurrentOrderIndex(nextOrderIndex);
            } else {
                // Reached end of orders, go back to collection details
                console.log('Reached end of orders, going back');
                navigation.goBack();
            }
        } else {
            console.log('Current order not completed, cannot proceed');
        }
    };



    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);




    // Animated style for slide transitions
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: slideAnimation.value * 300 }],
        };
    });

    // // Show skeleton while loading
    // if (loading) {
    //     return <FulfillmentSkeleton />;
    // }

    // Show error or no orders message
    if (!currentOrder) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">
                    {error ? `Error: ${error}` : processedOrders.length === 0 ? 'No orders found!' : 'No orders found'}
                </ThemedText>
                {processedOrders.length === 0 && (
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        style={{ marginTop: 20, padding: 10, backgroundColor: theme.primary, borderRadius: 8 }}
                    >
                        <ThemedText style={{ color: 'white' }}>Back to Collection</ThemedText>
                    </TouchableOpacity>
                )}
            </ThemedView>
        );
    }

    return (
        <ThemedView>
            {/* Header with return arrow and SKU */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                <ThemedText variant="title" style={styles.headerTitle}>
                    {currentOrder.orderNum}
                </ThemedText>
            </View>

            {/* Progress Line */}
            <View style={[styles.progressContainer, { borderBottomColor: theme.text + '50' }]}>
                <View style={styles.progressBackground}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${((currentOrderIndex + 1) / processedOrders.length) * 100}%`,
                                backgroundColor: theme.primary || '#007AFF'
                            }
                        ]}
                    />
                </View>
                <ThemedText variant="body" style={styles.progressText}>
                    Order {currentOrderIndex + 1} of {processedOrders.length}
                </ThemedText>
            </View>

            {/* Order Content */}
            <Animated.View style={[styles.content, animatedStyle]}>
                {/* Order Details */}
                <View style={[styles.orderDetailsCard, { backgroundColor: theme.cardBackground }]}>
                    <View style={styles.orderHeader}>
                        <View style={styles.orderInfo}>

                            <ThemedText variant="body" style={styles.orderCode}>
                                Code: {currentOrder.orderCode}
                            </ThemedText>
                            <ThemedText variant="body" style={styles.orderStatus}>
                                Status: {currentOrder.status}
                            </ThemedText>
                        </View>
                        <View style={styles.orderActions}>
                            <TouchableOpacity
                                style={[styles.iconButton, { backgroundColor: theme.primary }]}
                                onPress={handlePrintLabel}
                            >
                                <MaterialIcons name="print" size={24} color="white" />
                            </TouchableOpacity>
                        </View>
                    </View>


                </View>

                {/* Products List */}
                <View style={styles.productsContainer}>
                    {refreshing ?
                        <View style={styles.skeletonContainer}>
                            <ThreeDotsSkeleton />
                        </View> :
                        <ThemedText variant="subtitle" style={styles.productsTitle}>
                            Products ({currentOrder.ordersProducts.length})
                        </ThemedText>
                    }


                    <ScrollView
                        style={styles.productsList}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={onRefresh}
                                colors={[theme.primary || '#007AFF']}
                                tintColor={theme.primary || '#007AFF'}
                            />
                        }
                    >
                        {/* Show loading skeleton when individual products are being loaded */}


                        {currentOrder.ordersProducts.map((product, index) => {
                            const isChecked = product.status === 'gathered';
                            const isLoading = toggleLoading[product.id];

                            return (
                                <TouchableOpacity
                                    key={`${product.id}-${index}`}
                                    style={[styles.productRow, { borderBottomColor: theme.text + '50' }]}
                                    onPress={() => handleToggleCheckbox(product.id)}
                                    disabled={isLoading}
                                >
                                    <View style={styles.productQty}>
                                        <ThemedText variant="body" style={styles.qtyText}>
                                            {product.quantity} x
                                        </ThemedText>
                                    </View>

                                    <View style={styles.productName}>
                                        <ThemedText
                                            variant="body"
                                            style={[styles.nameText, isChecked && styles.checkedText]}
                                            numberOfLines={2}>
                                            {product.name}
                                        </ThemedText>
                                    </View>

                                    <View style={styles.productCheckbox}>
                                        {toggleLoading[product.id] ? (
                                            <ActivityIndicator size="small" color={theme.primary} />
                                        ) : (
                                            <Ionicons
                                                name={isChecked ? 'checkbox' : 'square-outline'}
                                                size={24}
                                                color={isChecked ? theme.success || 'green' : theme.text}
                                            />
                                        )}
                                    </View>
                                </TouchableOpacity>
                            );
                        })}
                        <TouchableOpacity
                            style={
                                {
                                    padding: 10,
                                    borderRadius: 8,
                                    marginVertical: 10,
                                    width: 100,
                                    alignSelf: 'flex-end',
                                    opacity: (() => {
                                        const currentOrderCompleted = !orderHasUngatheredProducts(currentOrder);
                                        return currentOrderCompleted ? 1 : 0.5;
                                    })()
                                }
                            }
                            onPress={handleNextOrder}
                            disabled={!orderHasUngatheredProducts(currentOrder) ? false : true}
                        >
                            <ThemedText style={[styles.nextButtonText, {
                                color: (() => {
                                    const currentOrderCompleted = !orderHasUngatheredProducts(currentOrder);
                                    return currentOrderCompleted ? theme.primary : 'gray';
                                })(),
                            }]}>
                                {currentOrderIndex === processedOrders.length - 1 ? 'Complete' : 'Next'}
                            </ThemedText>
                        </TouchableOpacity>
                    </ScrollView>

                    {/* Next Button */}

                </View>
            </Animated.View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
    },
    backButton: {
        padding: 8,
        marginRight: 16,
    },
    headerTitle: {
        flex: 1,
        textAlign: 'center',
        marginRight: 48, // Compensate for back button width
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    orderDetailsCard: {
        borderRadius: 12,
        padding: 16,
        marginVertical: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    orderHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    orderActions: {
        flexDirection: 'row',
        gap: 8,
    },
    iconButton: {
        width: 44,
        height: 44,
        borderRadius: 22,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goodsDescription: {
        fontSize: 14,
        lineHeight: 20,
        opacity: 0.8,
    },
    productsContainer: {
        flex: 1,
    },
    productsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    productsList: {
        flex: 1,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8,
        borderBottomWidth: 1,
    },
    productQty: {
        width: '20%',
        alignItems: 'center',
    },
    qtyText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    productName: {
        width: '70%',
        paddingHorizontal: 12,
    },
    nameText: {
        fontSize: 14,
        lineHeight: 18,
    },
    checkedText: {
        textDecorationLine: 'line-through',
        opacity: 0.6,
    },
    productCheckbox: {
        width: '10%',
        alignItems: 'center',
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 16,
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
    },
    orderIndicator: {
        flex: 1,
        alignItems: 'center',
    },
    indicatorText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    checkboxRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    skeletonContainer: {
        paddingVertical: 16,
        alignItems: 'center',
    },

    nextButtonText: {
        fontSize: 16,
        fontWeight: '600',
        margin: 'auto'
    },
});
