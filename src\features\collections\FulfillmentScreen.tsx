import React, { useEffect, useState } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Dimensions,
    ActivityIndicator,
    RefreshControl,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons, AntDesign, MaterialIcons } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withSpring,
    Easing,
} from 'react-native-reanimated';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import ThemedButton from '../../components/ThemedButton';
import useTheme from '../../theme/useTheme';
import { loadProgress } from '../../helpers/storage';
import { Order, OrderProduct } from '../../navigation/type';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders, toggleProductStatus } from '../orders/orderSlice';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';



import { CollectionStackParamList } from '../../navigation/type';

type FulfillmentRouteProp = RouteProp<CollectionStackParamList, 'Fulfillment'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'Fulfillment'>;



export default function FulfillmentScreen() {
    const { params } = useRoute<FulfillmentRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const { collectionId } = params;
    const dispatch = useDispatch<AppDispatch>();

    // Get orders from Redux state
    const { orders: reduxOrders, loading, error } = useSelector((state: RootState) => state.orders);

    // Local state for orders to avoid refetching on every toggle
    const [localOrders, setLocalOrders] = useState<Order[]>([]);
    const [currentSkuIndex, setCurrentSkuIndex] = useState(0);
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [refreshing, setRefreshing] = useState(false);

    // Animation values
    const slideAnimation = useSharedValue(0);

    // Process local orders
    const processedOrders = localOrders.map((order: Order) => ({
        ...order,
        ordersProducts: Array.isArray(order.ordersProducts) ? order.ordersProducts.map((product: OrderProduct) => ({
            ...product,
            status: product.status || 'pending'
        })) : []
    }));

    const groupedOrders = processedOrders.reduce((acc: { [sku: string]: Order[] }, order: Order) => {
        const sku = order.sku || 'Unknown';
        if (!acc[sku]) acc[sku] = [];
        acc[sku].push(order);
        return acc;
    }, {});

    // Sort SKUs alphabetically and create the list
    const skuList = Object.entries(groupedOrders)
        .sort(([skuA], [skuB]) => skuA.localeCompare(skuB))
        .map(([sku, skuOrders]) => ({ sku, orders: skuOrders }));
    const currentSku = skuList[currentSkuIndex];
    const currentOrder = currentSku?.orders[currentOrderIndex];



    // Helper function to check if an order needs checking (has products that are not gathered)
    const orderNeedsChecking = (order: Order): boolean => {
        return order.ordersProducts.some(product => product.status !== 'gathered');
    };

    // Helper function to find the first order that needs checking in a specific SKU
    const findFirstOrderNeedingCheckInSku = (sku: { sku: string; orders: Order[] }): number => {
        for (let orderIndex = 0; orderIndex < sku.orders.length; orderIndex++) {
            const order = sku.orders[orderIndex];
            if (orderNeedsChecking(order)) {
                return orderIndex;
            }
        }
        return 0; // If no orders need checking, start from first order
    };

    // Helper function to check if a SKU group has any orders that need checking
    const skuNeedsChecking = (sku: { sku: string; orders: Order[] }): boolean => {
        return sku.orders.some(order => orderNeedsChecking(order));
    };

    // Helper function to find the first SKU that has orders needing checking
    const findFirstSkuNeedingCheck = (): number => {
        for (let skuIndex = 0; skuIndex < skuList.length; skuIndex++) {
            if (skuNeedsChecking(skuList[skuIndex])) {
                return skuIndex;
            }
        }
        return 0; // If no SKUs need checking, start from first SKU
    };

    // Fetch orders on component mount
    useEffect(() => {
        dispatch(fetchOrders(collectionId));
    }, [dispatch, collectionId]);

    // Sync Redux orders with local state
    useEffect(() => {
        if (reduxOrders.length > 0) {
            setLocalOrders(reduxOrders);
        }
    }, [reduxOrders]);

    // Start from the first SKU that needs checking when orders are loaded
    useEffect(() => {
        if (localOrders.length > 0 && skuList.length > 0) {
            // Find the first SKU that has orders needing checking
            const firstSkuIndex = findFirstSkuNeedingCheck();
            setCurrentSkuIndex(firstSkuIndex);

            // Find the first order that needs checking in that SKU
            const targetSku = skuList[firstSkuIndex];
            if (targetSku) {
                const firstOrderIndex = findFirstOrderNeedingCheckInSku(targetSku);
                setCurrentOrderIndex(firstOrderIndex);
            }
        }
    }, [localOrders.length, skuList.length]);


    // Loading state for individual products
    const [toggleLoading, setToggleLoading] = useState<{ [productId: number]: boolean }>({});

    // Refresh function for pull-to-refresh
    const onRefresh = async () => {
        setRefreshing(true);
        try {
            await dispatch(fetchOrders(collectionId));
        } catch (error) {
            console.error('Error refreshing orders:', error);
        } finally {
            setRefreshing(false);
        }
    };

    const handleToggleCheckbox = async (productId: number) => {
        setToggleLoading((prev) => ({ ...prev, [productId]: true }));

        try {
            const res = await dispatch(toggleProductStatus({ productId }));
            if (toggleProductStatus.fulfilled.match(res)) {
                // Update local state instead of refetching all orders
                setLocalOrders(prevOrders =>
                    prevOrders.map(order => ({
                        ...order,
                        ordersProducts: order.ordersProducts.map(product =>
                            product.id === productId
                                ? { ...product, status: res.payload }
                                : product
                        )
                    }))
                );

            }
        } catch (error) {
            console.error('Error toggling product status:', error);
        } finally {
            setToggleLoading((prev) => ({ ...prev, [productId]: false }));
        }
    };

    // Check if all products in current order are checked (gathered)
    const allCurrentOrderChecked = currentOrder?.ordersProducts.every(p =>
        p.status === 'gathered'
    ) || false;

    const handlePrintLabel = () => {
        // TODO: Implement print functionality
        console.log('Print label for order:', currentOrder?.orderNum);
    };

    const handleNextOrder = () => {
        if (!currentSku) return;

        const nextOrderIndex = currentOrderIndex + 1;

        if (nextOrderIndex < currentSku.orders.length) {
            // Move to next order in current SKU with animation
            setCurrentOrderIndex(nextOrderIndex);
        } else {
            // Move to next SKU
            handleNextSku();
        }
    };

    const handlePreviousOrder = () => {
        if (currentOrderIndex > 0) {
            setCurrentOrderIndex(currentOrderIndex - 1);
        }
    };

    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);

    const handleNextSku = () => {
        // Find the next SKU that needs checking starting from current index + 1
        let nextSkuIndex = -1;
        for (let i = currentSkuIndex + 1; i < skuList.length; i++) {
            if (skuNeedsChecking(skuList[i])) {
                nextSkuIndex = i;
                break;
            }
        }

        console.log('Current SKU Index:', currentSkuIndex);
        console.log('Next SKU Index that needs checking:', nextSkuIndex);
        console.log('Total SKUs:', skuList.length);
        console.log('SKU List:', skuList.map(item => item.sku));

        if (nextSkuIndex !== -1) {
            console.log('Moving to next SKU that needs checking:', skuList[nextSkuIndex].sku);
            setCurrentSkuIndex(nextSkuIndex);

            // Auto-skip to first order that needs checking in the new SKU
            const newSku = skuList[nextSkuIndex];
            const firstOrderIndex = findFirstOrderNeedingCheckInSku(newSku);
            setCurrentOrderIndex(firstOrderIndex);
        } else {
            console.log('No more SKUs need checking, going back');
            navigation.goBack();
        }
    };


    // Animated style for slide transitions
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: slideAnimation.value * 300 }],
        };
    });

    // // Show skeleton while loading
    // if (loading) {
    //     return <FulfillmentSkeleton />;
    // }

    // Show error or no orders message
    if (!currentSku || !currentOrder) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">
                    {error ? `Error: ${error}` : 'No orders found'}
                </ThemedText>
            </ThemedView>
        );
    }

    return (
        <ThemedView>
            {/* Header with return arrow and SKU */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                <ThemedText variant="title" style={styles.headerTitle}>
                    SKU: {currentSku.sku}
                </ThemedText>
            </View>

            {/* Progress Line */}
            <View style={[styles.progressContainer, { borderBottomColor: theme.text + '50' }]}>
                <View style={styles.progressBackground}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${((currentOrderIndex + 1) / currentSku.orders.length) * 100}%`,
                                backgroundColor: theme.primary || '#007AFF'
                            }
                        ]}
                    />
                </View>
                <ThemedText variant="body" style={styles.progressText}>
                    Order {currentOrderIndex + 1} of {currentSku.orders.length}
                </ThemedText>
            </View>

            {/* Order Content */}
            <Animated.View style={[styles.content, animatedStyle]}>
                {/* Order Details */}
                <View style={[styles.orderDetailsCard, { backgroundColor: theme.cardBackground }]}>
                    <View style={styles.orderHeader}>
                        <View style={styles.orderInfo}>
                            <ThemedText variant="subtitle" style={styles.orderNumber}>
                                Order #{currentOrder.orderNum}
                            </ThemedText>
                            <ThemedText variant="body" style={styles.orderCode}>
                                Code: {currentOrder.orderCode}
                            </ThemedText>
                            <ThemedText variant="body" style={styles.orderStatus}>
                                Status: {currentOrder.status}
                            </ThemedText>
                        </View>
                        <View style={styles.orderActions}>
                            <TouchableOpacity
                                style={[styles.iconButton, { backgroundColor: theme.primary }]}
                                onPress={handlePrintLabel}
                            >
                                <MaterialIcons name="print" size={24} color="white" />
                            </TouchableOpacity>
                        </View>
                    </View>

                    {currentOrder.goodsDescription && (
                        <ThemedText variant="body" style={styles.goodsDescription}>
                            {currentOrder.goodsDescription.replace(/<br>/g, '\n')}
                        </ThemedText>
                    )}
                </View>

                {/* Products List */}
                <View style={styles.productsContainer}>
                    {refreshing ?
                        <View style={styles.skeletonContainer}>
                            <ThreeDotsSkeleton />
                        </View> :
                        <ThemedText variant="subtitle" style={styles.productsTitle}>
                            Products ({currentOrder.ordersProducts.length})
                        </ThemedText>
                    }


                    <ScrollView
                        style={styles.productsList}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={onRefresh}
                                colors={[theme.primary || '#007AFF']}
                                tintColor={theme.primary || '#007AFF'}
                            />
                        }
                    >
                        {/* Show loading skeleton when individual products are being loaded */}


                        {currentOrder.ordersProducts.map((product, index) => {
                            const isChecked = product.status === 'gathered';
                            const isLoading = toggleLoading[product.id];

                            return (
                                <TouchableOpacity
                                    key={`${product.id}-${index}`}
                                    style={[styles.productRow, { borderBottomColor: theme.text + '50' }]}
                                    onPress={() => handleToggleCheckbox(product.id)}
                                    disabled={isLoading}
                                >
                                    <View style={styles.productQty}>
                                        <ThemedText variant="body" style={styles.qtyText}>
                                            {product.quantity} x
                                        </ThemedText>
                                    </View>

                                    <View style={styles.productName}>
                                        <ThemedText
                                            variant="body"
                                            style={[styles.nameText, isChecked && styles.checkedText]}
                                            numberOfLines={2}>
                                            {product.name}
                                        </ThemedText>
                                    </View>

                                    <View style={styles.productCheckbox}>
                                        {toggleLoading[product.id] ? (
                                            <ActivityIndicator size="small" color={theme.primary} />
                                        ) : (
                                            <Ionicons
                                                name={isChecked ? 'checkbox' : 'square-outline'}
                                                size={24}
                                                color={isChecked ? theme.success || 'green' : theme.text}
                                            />
                                        )}
                                    </View>
                                </TouchableOpacity>
                            );
                        })}
                    </ScrollView>
                </View>
            </Animated.View>

            {/* Footer Navigation */}
            <View style={[styles.footer, { borderTopColor: theme.text + '50' }]}>
                <TouchableOpacity style={{ borderColor: currentOrderIndex === 0 ? 'gray' : theme.primary, borderWidth: 3, borderRadius: 8 }} onPress={handlePreviousOrder} disabled={currentOrderIndex === 0}>
                    <AntDesign name="arrowleft" size={30} color={currentOrderIndex === 0 ? 'gray' : theme.primary} />
                </TouchableOpacity>

                <TouchableOpacity style={{ borderColor: allCurrentOrderChecked ? theme.primary : 'gray', borderWidth: 3, borderRadius: 8 }} onPress={handleNextOrder} disabled={!allCurrentOrderChecked}>
                    <AntDesign name="arrowright" size={30} color={allCurrentOrderChecked ? theme.primary : 'gray'} />
                </TouchableOpacity>

                {/*                 
                <ThemedButton
                    title="Back"
                    onPress={handlePreviousOrder}
                    disabled={currentOrderIndex === 0}
                />

                <View style={styles.orderIndicator}>
                    <ThemedText variant="body" style={styles.indicatorText}>
                        {currentOrderIndex + 1} / {currentSku.orders.length}
                    </ThemedText>
                </View>

                <ThemedButton
                    title="Next"
                    onPress={handleNextOrder}
                    disabled={!allCurrentOrderChecked}
                /> */}
            </View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
    },
    backButton: {
        padding: 8,
        marginRight: 16,
    },
    headerTitle: {
        flex: 1,
        textAlign: 'center',
        marginRight: 48, // Compensate for back button width
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    orderDetailsCard: {
        borderRadius: 12,
        padding: 16,
        marginVertical: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    orderHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    orderActions: {
        flexDirection: 'row',
        gap: 8,
    },
    iconButton: {
        width: 44,
        height: 44,
        borderRadius: 22,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goodsDescription: {
        fontSize: 14,
        lineHeight: 20,
        opacity: 0.8,
    },
    productsContainer: {
        flex: 1,
    },
    productsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    productsList: {
        flex: 1,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8,
        borderBottomWidth: 1,
    },
    productQty: {
        width: '20%',
        alignItems: 'center',
    },
    qtyText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    productName: {
        width: '70%',
        paddingHorizontal: 12,
    },
    nameText: {
        fontSize: 14,
        lineHeight: 18,
    },
    checkedText: {
        textDecorationLine: 'line-through',
        opacity: 0.6,
    },
    productCheckbox: {
        width: '10%',
        alignItems: 'center',
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 16,
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
    },
    orderIndicator: {
        flex: 1,
        alignItems: 'center',
    },
    indicatorText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    checkboxRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    skeletonContainer: {
        paddingVertical: 16,
        alignItems: 'center',
    },
});
