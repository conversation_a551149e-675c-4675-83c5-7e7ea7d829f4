import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../features/auth/authSlice';
import collectionReducer from '../features/collections/collectionSlice';
import warehouseReducer from '../features/warehouses/warehouseSlice';
import orderReducer from '../features/orders/orderSlice';
// import productReducer from '../features/products/productSlice';

export const store = configureStore({
    reducer: {
        auth: authReducer,
        collections: collectionReducer,
        warehouses: warehouseReducer,
        orders: orderReducer,
        // products: productReducer,

    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
