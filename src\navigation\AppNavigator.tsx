import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../features/auth/LoginScreen';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import MainAppScreen from '../screens/MainAppScreen';
import { RootStackParamList } from './type';
import AppSplashScreen from '../screens/SplashScreen';


const Stack = createNativeStackNavigator<RootStackParamList>();

export default function AppNavigator() {
    const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

    return (
        <NavigationContainer>
            <Stack.Navigator screenOptions={{ headerShown: false }}>
                {isLoggedIn ? (
                    <Stack.Screen
                        name="MainApp"
                        component={MainAppScreen}
                        options={{ gestureEnabled: false }}
                    />
                ) : (
                    <>
                        <Stack.Screen name="Splash" component={AppSplashScreen} />
                        <Stack.Screen name="Login" component={LoginScreen} />
                    </>
                )}
            </Stack.Navigator>
        </NavigationContainer>
    );
}
