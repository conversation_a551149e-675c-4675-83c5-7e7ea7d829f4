import React, { useState, useEffect, useRef } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainerRef } from '@react-navigation/native';
import MainLayout from '../components/MainLayout';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import ReturnsScreen from '../features/returns/ReturnsScreen';
import { CollectionStackParamList } from '../navigation/type';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';

const Stack = createNativeStackNavigator<CollectionStackParamList>();

type TabType = 'collections' | 'returns';

export default function MainAppScreen() {
    const [activeTab, setActiveTab] = useState<TabType>('collections');
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const navigationRef = useRef<NavigationContainerRef<CollectionStackParamList>>(null);
    const [previousWarehouseId, setPreviousWarehouseId] = useState<number | null>(null);

    // Reset navigation to CollectionList when warehouse changes
    useEffect(() => {
        if (selectedWarehouse && selectedWarehouse.id !== previousWarehouseId) {
            console.log('Warehouse changed, resetting navigation to CollectionList');

            // Reset navigation to collection list
            if (navigationRef.current && activeTab === 'collections') {
                navigationRef.current.reset({
                    index: 0,
                    routes: [{ name: 'CollectionList' }],
                });
            }

            setPreviousWarehouseId(selectedWarehouse.id);
        }
    }, [selectedWarehouse, previousWarehouseId, activeTab]);

    const handleTabChange = (tab: TabType) => {
        setActiveTab(tab);

        // If switching to collections tab, reset to collection list
        if (tab === 'collections' && navigationRef.current) {
            navigationRef.current.reset({
                index: 0,
                routes: [{ name: 'CollectionList' }],
            });
        }
    };

    const renderTabContent = () => {
        if (activeTab === 'returns') {
            return <ReturnsScreen />;
        }

        // For collections tab, we need to handle the navigation stack
        return (
            <Stack.Navigator
                ref={navigationRef}
                screenOptions={{ headerShown: false }}
                initialRouteName="CollectionList"
            >
                <Stack.Screen name="CollectionList" component={CollectionListScreen} />
                <Stack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
                <Stack.Screen name="Fulfillment" component={FulfillmentScreen} />
            </Stack.Navigator>
        );
    };

    return (
        <MainLayout activeTab={activeTab} onTabChange={handleTabChange}>
            {renderTabContent()}
        </MainLayout>
    );
}
