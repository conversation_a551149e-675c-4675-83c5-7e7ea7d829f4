// features/auth/authThunks.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import { loginSuccess } from './authSlice';
import axiosClient from '../../api/axiosClient';

export const loginUser = createAsyncThunk(
    'auth/loginUser',
    async (
        credentials: { email: string; password: string },
        { dispatch, rejectWithValue }
    ) => {
        try {
            const response = await axiosClient.post('/login', credentials);
            const { token, user } = response.data;

            // Save token to localStorage or AsyncStorage if needed
            dispatch(loginSuccess({ token, user }));

            return { token, user };
        } catch (err: any) {
            return rejectWithValue(
                err.response?.data?.message || 'Login failed'
            );
        }
    }
);
