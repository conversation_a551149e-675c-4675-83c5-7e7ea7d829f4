{"name": "warehouse-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "dayjs": "^1.11.13", "expo": "~53.0.17", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-modalize": "^2.1.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-timeline-flatlist": "^0.8.0", "react-redux": "^9.2.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}