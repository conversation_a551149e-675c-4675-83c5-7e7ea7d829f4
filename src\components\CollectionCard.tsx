
import React from 'react';
import { GestureResponderEvent, StyleSheet, TouchableOpacity, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import StatusRenderer from './StatusRenderer';
import { FontAwesome, FontAwesome6 } from '@expo/vector-icons';
dayjs.extend(relativeTime);

type Props = {
    collection: any;
    onPress: (event: GestureResponderEvent) => void;
};

export default function CollectionCard({ collection, onPress }: Props) {
    const theme = useTheme();

    return (
        <TouchableOpacity
            onPress={onPress}
            style={[styles.button, { backgroundColor: theme.cardBackground }]}
        >
            <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                <ThemedText variant='title'>{collection.num}</ThemedText>
                <View style={{ display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                    <FontAwesome name="cube" size={20} color={theme.text} />
                    <ThemedText variant='body'>{collection.orders}</ThemedText>
                </View>
            </View>

            <View style={{ flex: 1, width: '100%', marginTop: 10, display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                <ThemedText variant='body'>{dayjs(collection.addedAt).fromNow()}</ThemedText>

                <StatusRenderer status={collection.status} />

            </View>

        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        alignItems: 'center',
    },
    text: {
        fontWeight: 'bold',
        fontSize: 16,
    },
});
