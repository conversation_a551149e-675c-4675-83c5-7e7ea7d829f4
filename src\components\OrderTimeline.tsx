import React from 'react';
import { View, StyleSheet, FlatList, Animated } from 'react-native';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';

type Props = {
    orders: { id: number; orderNum: string }[];
    gatheredOrders: { [orderId: number]: boolean };
};

export default function OrderTimeline({ orders, gatheredOrders }: Props) {
    const theme = useTheme();
    return (
        <View style={styles.container}>
            <FlatList
                data={orders}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                renderItem={({ item, index }) => {
                    const isDone = gatheredOrders[item.id];
                    return (
                        <View style={styles.row}>
                            {/* Line + Dot */}
                            <View style={styles.lineContainer}>
                                {/* Line above */}
                                {index > 0 && <View style={styles.line} />}
                                <View
                                    style={[
                                        styles.dot,
                                        { backgroundColor: isDone ? theme.success : theme.primary }
                                    ]}
                                />
                                {/* Line below */}
                                {index < orders.length - 1 && <View style={{ ...styles.line, backgroundColor: isDone ? theme.success : theme.primary }} />}
                            </View>

                            {/* Label */}
                            <View style={styles.labelContainer}>
                                <ThemedText style={{ opacity: isDone ? 1 : 0.4 }}>
                                    Order {item.orderNum} — {isDone ? 'Ready' : 'Pending'}
                                </ThemedText>
                            </View>
                        </View>
                    );
                }}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingVertical: 10,
        paddingLeft: 10,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 20,
    },
    lineContainer: {
        alignItems: 'center',
        width: 30,
    },
    dot: {
        width: 12,
        height: 12,
        borderRadius: 6,
        zIndex: 1,
    },
    line: {
        width: 3,
        flex: 1,
    },
    labelContainer: {
        paddingLeft: 10,
        justifyContent: 'center',
        flex: 1,
    },
});
