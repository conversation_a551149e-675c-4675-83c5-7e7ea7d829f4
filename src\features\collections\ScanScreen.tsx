import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    ActivityIndicator,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { CameraView, Camera } from 'expo-camera';
import { Audio } from 'expo-av';
import { useAudioPlayer } from 'expo-audio';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withRepeat,
} from 'react-native-reanimated';
import Svg, { Rect, Text as SvgText } from 'react-native-svg';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { Order } from '../../navigation/type';
import { CollectionStackParamList } from '../../navigation/type';
import { BarcodeView } from 'rn-barcode-renderer';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { changeOrderStatus } from '../orders/orderSlice';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';

const SuccessAudioSource = require('../../../assets/sounds/success.mp3');
const ErrorAudioSource = require('../../../assets/sounds/error.mp3');
type ScanRouteProp = RouteProp<CollectionStackParamList, 'ScanScreen'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'ScanScreen'>;

// Simple barcode component using SVG
const SimpleBarcode = ({ value, width = 200, height = 60 }: { value: string; width?: number; height?: number }) => {
    // Simple barcode pattern generator (basic representation)
    const generateBarcodePattern = (text: string) => {
        const patterns: number[] = [];
        // Create a simple pattern based on character codes
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            // Create varying bar widths based on character code
            patterns.push(charCode % 4 + 1); // Bar width 1-4
            patterns.push(1); // Space width 1
        }
        return patterns;
    };

    const pattern = generateBarcodePattern(value);
    const totalWidth = pattern.reduce((sum, width) => sum + width, 0);
    const scale = (width - 40) / totalWidth; // Leave margin for text

    let currentX = 20;
    const bars: React.ReactElement[] = [];

    pattern.forEach((barWidth, index) => {
        if (index % 2 === 0) { // Even indices are bars
            bars.push(
                <Rect
                    key={index}
                    x={currentX}
                    y={10}
                    width={barWidth * scale}
                    height={height - 30}
                    fill="#000"
                />
            );
        }
        currentX += barWidth * scale;
    });

    return (
        <View style={{ alignItems: 'center' }}>
            {/* <Svg width={width} height={height}>
                {bars}
                <SvgText
                    x={width / 2}
                    y={height - 5}
                    fontSize="12"
                    textAnchor="middle"
                    fill="#000"
                >
                    {value}
                </SvgText>
            </Svg> */}
            <BarcodeView value={value} format="CODE128" maxWidth={200} height={100} />
            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                {value}
            </ThemedText>
        </View>
    );
};

export default function ScanScreen() {
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const [torchOn, setTorchOn] = useState(false);
    // State management
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [scanned, setScanned] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const [scanSuccess, setScanSuccess] = useState(false);
    const [scanError, setScanError] = useState<string | null>(null);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const prevWarehouseId = useRef<number | null>(null);
    const { loading: ordersLoading, orders } = useSelector((state: RootState) => state.orders);
    const dispatch = useDispatch<AppDispatch>();

    useEffect(() => {
        if (selectedWarehouse?.id && prevWarehouseId.current && selectedWarehouse.id !== prevWarehouseId.current) {
            navigation.navigate('CollectionList');
        }

        if (selectedWarehouse?.id) {
            prevWarehouseId.current = selectedWarehouse.id;
        }
    }, [selectedWarehouse?.id]);

    const scanLineTranslateY = useSharedValue(0);

    useEffect(() => {
        scanLineTranslateY.value = withRepeat(
            withTiming(240, { duration: 1000 }),
            -1,
            true
        );
    }, []);
    const scanLineStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateY: scanLineTranslateY.value }],
        };
    });


    const toggleTorch = () => {
        setTorchOn((prev) => !prev);
    };

    useEffect(() => {

    }, [orders])


    // Animation values
    const slideAnimation = useSharedValue(0);

    // Process orders - only show orders with all products gathered
    const processedOrders = orders
        .filter(order => order.ordersProducts.every(product => product.status === 'gathered'))
        .sort((a, b) => a.orderNum.localeCompare(b.orderNum));

    const currentOrder = processedOrders[currentOrderIndex];
    const SuccessPlayer = useAudioPlayer(SuccessAudioSource);
    const ErrorPlayer = useAudioPlayer(ErrorAudioSource);


    // Request camera permissions
    useEffect(() => {
        const getCameraPermissions = async () => {
            const { status } = await Camera.requestCameraPermissionsAsync();
            setHasPermission(status === 'granted');
        };

        getCameraPermissions();
    }, []);

    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);



    // Animated style for slide transitions
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: slideAnimation.value * 300 }],
        };
    });

    // Audio feedback functions
    const playSuccessSound = async () => {
        try {
            SuccessPlayer.seekTo(0);
            SuccessPlayer.play();
        } catch (err) {
            console.warn('Failed to play success sound', err);
        }
    };

    const playErrorSound = async () => {
        try {
            ErrorPlayer.seekTo(0);
            ErrorPlayer.play();
        } catch (err) {
            console.warn('Failed to play error sound', err);
        }
    };



    const handleBarcodeScanned = async (scanningResult: { type: string; data: string }) => {
        setScanned(true);
        setIsScanning(false);


        if (scanningResult.data === currentOrder.orderCode) {

            await dispatch(changeOrderStatus({ orderId: currentOrder.id }));
            // Success - show success state
            setScanSuccess(true);
            setScanError(null);
            playSuccessSound();


        } else {
            // Failed - show error state
            setScanSuccess(false);
            setScanError(`Expected: ${currentOrder.orderCode}\nScanned: ${scanningResult.data}`);
            playErrorSound();
        }
    };

    const handleNextOrder = () => {
        const nextOrderIndex = currentOrderIndex + 1;

        if (nextOrderIndex < processedOrders.length) {
            setCurrentOrderIndex(nextOrderIndex);
            resetScanState();
        } else {
            // All orders scanned, go back to collection details
            navigation.goBack();
        }
    };

    const resetScanState = () => {
        setScanned(false);
        setScanSuccess(false);
        setScanError(null);

        // Don't reset isScanning here - it should be controlled separately
    };

    const startScanning = () => {

        if (hasPermission === true) {
            resetScanState(); // Reset other states first
            setIsScanning(true); // Then set scanning to 

        } else {

            // Re-request permissions if not granted
            Camera.requestCameraPermissionsAsync().then(({ status }) => {
                setHasPermission(status === 'granted');
                if (status === 'granted') {
                    setIsScanning(true);
                    resetScanState();
                }
            });
        }
    };

    const retryScanning = () => {
        resetScanState();
        setIsScanning(true);
    };

    // Show error or no orders message
    if (!currentOrder) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">
                    {processedOrders.length === 0 ? 'No orders ready for scanning!' : 'No orders found'}
                </ThemedText>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={{ marginTop: 20, padding: 10, backgroundColor: theme.primary, borderRadius: 8 }}
                >
                    <ThemedText style={{ color: 'white' }}>Back to Collection</ThemedText>
                </TouchableOpacity>
            </ThemedView>
        );
    }

    if (hasPermission === null) {

        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">Requesting camera permission...</ThemedText>
            </ThemedView>
        );
    }

    if (hasPermission === false) {

        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">No access to camera</ThemedText>
                <ThemedText variant="body" style={{ textAlign: 'center', marginTop: 10 }}>
                    Camera permission is required to scan barcodes
                </ThemedText>
            </ThemedView>
        );
    }

    return (
        <ThemedView style={styles.container}>
            {/* Header with return arrow and order info */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                {ordersLoading ? <ThreeDotsSkeleton /> : <ThemedText variant="title" style={styles.headerTitle}>
                    Scan Order: {currentOrder.orderNum}
                </ThemedText>}
            </View>

            {/* Progress Line */}
            <View style={[styles.progressContainer, { borderBottomColor: theme.text + '50' }]}>
                <View style={styles.progressBackground}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${((currentOrderIndex + 1) / processedOrders.length) * 100}%`,
                                backgroundColor: theme.primary || '#007AFF'
                            }
                        ]}
                    />
                </View>
                <ThemedText variant="body" style={styles.progressText}>
                    Order {currentOrderIndex + 1} of {processedOrders.length}
                </ThemedText>
            </View>

            {/* Scan Content */}
            <Animated.View style={[styles.content, animatedStyle]}>
                {/* Debug Info */}

                {scanError && (
                    <View style={[styles.errorContainer, { backgroundColor: '#ffebee' }]}>
                        <View style={styles.errorHeader}>
                            <AntDesign name="exclamationcircle" size={24} color="#f44336" />
                            <ThemedText variant="subtitle" style={[styles.errorTitle, { color: '#f44336' }]}>
                                Wrong Barcode
                            </ThemedText>
                        </View>
                        <ThemedText variant="body" style={[styles.errorMessage, { color: '#d32f2f' }]}>
                            {scanError}
                        </ThemedText>
                        <TouchableOpacity
                            style={[styles.retryButton, { borderColor: theme.primary }]}
                            onPress={retryScanning}
                        >
                            <ThemedText style={[styles.retryButtonText, { color: theme.primary }]}>
                                Try Again
                            </ThemedText>
                        </TouchableOpacity>
                    </View>
                )}
                {/* Order Details */}
                {/* <View style={[styles.orderDetailsCard, { backgroundColor: theme.cardBackground }]}>
                    <View style={styles.orderInfo}>
                        <ThemedText variant="subtitle" style={styles.orderNumber}>
                            Order: {currentOrder.orderNum}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderCode}>
                            Expected Code: {currentOrder.orderCode}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderStatus}>
                            Status: {currentOrder.status}
                        </ThemedText>
                    </View>
                </View> */}

                {/* Scanner, Success Card, or Scan Button */}
                <View style={styles.scanContainer}>
                    {(() => {
                        if (isScanning) {
                            return (
                                <View style={styles.scannerContainer}>

                                    <CameraView
                                        enableTorch={torchOn}
                                        style={styles.scanner}
                                        facing="back"
                                        barcodeScannerSettings={{
                                            barcodeTypes: ["qr", "pdf417", "code128", "code39", "code93", "codabar", "ean13", "ean8", "upc_e", "upc_a"],
                                        }}
                                        onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
                                    />


                                    <View style={styles.scanOverlay}>
                                        <TouchableOpacity
                                            style={styles.torchButton}
                                            onPress={toggleTorch}
                                        >
                                            <Ionicons name={torchOn ? 'flash' : 'flash-off'} size={24} color="white" />
                                        </TouchableOpacity>
                                        <View style={styles.scanFrame} >

                                            <Animated.View style={[styles.scanLine, scanLineStyle]}>
                                                <LinearGradient
                                                    colors={['transparent', 'rgba(255,0,0,0.7)', 'transparent']}
                                                    style={{ flex: 1 }}
                                                    start={[0, 0]}
                                                    end={[1, 0]}
                                                />
                                            </Animated.View>
                                            <BarcodeView value={currentOrder.orderCode} format="CODE128" color={theme.primary} bgColor='transparent' maxWidth={200} height={100} />

                                        </View>

                                    </View>
                                </View>
                            );
                        } else if (scanSuccess) {
                            return (
                                // Success Card with Generated Barcode
                                <View style={[styles.successCard, { backgroundColor: theme.cardBackground }]}>
                                    <View style={styles.successHeader}>
                                        <AntDesign name="checkcircle" size={32} color="#4CAF50" />
                                        <ThemedText variant="title" style={[styles.successTitle, { color: '#4CAF50' }]}>
                                            Scan Successful!
                                        </ThemedText>
                                    </View>

                                    <ThemedText variant="body" style={styles.successMessage}>
                                        Order {currentOrder.orderNum} scanned successfully
                                    </ThemedText>

                                    {/* Generated Barcode */}
                                    <View style={styles.barcodeContainer}>
                                        <SimpleBarcode
                                            value={currentOrder.orderCode}
                                            width={250}
                                            height={80}
                                        />
                                    </View>

                                    <TouchableOpacity
                                        style={[styles.continueButton, { backgroundColor: theme.primary }]}
                                        onPress={handleNextOrder}
                                    >
                                        <ThemedText style={[styles.continueButtonText, { color: 'white' }]}>
                                            {currentOrderIndex === processedOrders.length - 1 ? 'Complete' : 'Continue'}
                                        </ThemedText>
                                    </TouchableOpacity>
                                </View>
                            );
                        } else {
                            return (
                                <View style={styles.scanButtonContainer}>
                                    <TouchableOpacity
                                        style={[styles.scanButton, { backgroundColor: theme.primary }]}
                                        onPress={() => {
                                            startScanning();
                                        }}
                                    >
                                        <MaterialCommunityIcons name="barcode-scan" size={48} color="white" />
                                        <ThemedText style={[styles.scanButtonText, { color: 'white' }]}>
                                            Scan Barcode
                                        </ThemedText>
                                    </TouchableOpacity>


                                </View>
                            );
                        }
                    })()}
                </View>

                {/* Error Display */}

            </Animated.View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scanLine: {
        position: 'absolute',
        width: '90%',
        zIndex: 1,
        height: 4,
        borderRadius: 2,
        overflow: 'hidden',
        top: 0,
        alignSelf: 'center',
    },
    torchButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
    },
    backButton: {
        padding: 8,
        marginRight: 16,
    },
    headerTitle: {
        flex: 1,
        textAlign: 'center',
        marginRight: 48, // Compensate for back button width
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    orderDetailsCard: {
        borderRadius: 12,
        padding: 16,
        marginVertical: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    scanContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButtonContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButton: {
        width: 200,
        height: 200,
        borderRadius: 100,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    scanButtonText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 8,
    },
    scannerContainer: {
        flex: 1,
        width: '100%',
        position: 'relative',
    },
    scanner: {
        flex: 1,
    },
    scanOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanFrame: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#007AFF',
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    scanInstructions: {
        marginTop: 20,
        textAlign: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
    successCard: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 24,
        borderRadius: 16,
        marginHorizontal: 16,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    successHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    successTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 12,
    },
    successMessage: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 24,
        opacity: 0.8,
    },
    barcodeContainer: {
        backgroundColor: '#fff',
        padding: 16,
        borderRadius: 8,
        marginBottom: 24,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    continueButton: {
        paddingVertical: 12,
        paddingHorizontal: 32,
        borderRadius: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    continueButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    errorContainer: {
        margin: 16,
        padding: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#ffcdd2',
    },
    errorHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    errorTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    errorMessage: {
        fontSize: 14,
        marginBottom: 16,
        lineHeight: 20,
    },
    retryButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 6,
        borderWidth: 1,
        alignSelf: 'flex-start',
    },
    retryButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
});
