import React, { useEffect, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign } from '@expo/vector-icons';
import { CameraView, Camera } from 'expo-camera';
import { Audio } from 'expo-av';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
} from 'react-native-reanimated';
// @ts-ignore
import Barcode from 'react-native-barcode-builder';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { Order } from '../../navigation/type';
import { CollectionStackParamList } from '../../navigation/type';

type ScanRouteProp = RouteProp<CollectionStackParamList, 'ScanScreen'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'ScanScreen'>;

export default function ScanScreen() {
    const { params } = useRoute<ScanRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const { collectionId, orders } = params;

    // State management
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [scanned, setScanned] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const [scanSuccess, setScanSuccess] = useState(false);
    const [scanError, setScanError] = useState<string | null>(null);
    const [scannedData, setScannedData] = useState<string>('');

    // Animation values
    const slideAnimation = useSharedValue(0);

    // Process orders - only show orders with all products gathered
    const processedOrders = orders
        .filter(order => order.ordersProducts.every(product => product.status === 'gathered'))
        .sort((a, b) => a.orderNum.localeCompare(b.orderNum));

    const currentOrder = processedOrders[currentOrderIndex];

    // Request camera permissions
    useEffect(() => {
        const getCameraPermissions = async () => {
            const { status } = await Camera.requestCameraPermissionsAsync();
            setHasPermission(status === 'granted');
        };

        getCameraPermissions();
    }, []);

    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);

    // Animated style for slide transitions
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: slideAnimation.value * 300 }],
        };
    });

    // Audio feedback functions
    const playSuccessSound = async () => {
        try {
            const { sound } = await Audio.Sound.createAsync(
                { uri: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT' } as any,
                { shouldPlay: false }
            );
            await sound.playAsync();
            sound.unloadAsync();
        } catch (error) {
            console.log('Error playing success sound:', error);
        }
    };

    const playErrorSound = async () => {
        try {
            const { sound } = await Audio.Sound.createAsync(
                { uri: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT' } as any,
                { shouldPlay: false }
            );
            await sound.playAsync();
            sound.unloadAsync();
        } catch (error) {
            console.log('Error playing error sound:', error);
        }
    };

    const handleBarcodeScanned = (scanningResult: { type: string; data: string }) => {
        setScanned(true);
        setIsScanning(false);
        setScannedData(scanningResult.data);

        console.log('Scanned barcode:', scanningResult.data);
        console.log('Expected orderCode:', currentOrder.orderCode);

        if (scanningResult.data === currentOrder.orderCode) {
            // Success - show success state
            setScanSuccess(true);
            setScanError(null);
            playSuccessSound();
        } else {
            // Failed - show error state
            setScanSuccess(false);
            setScanError(`Expected: ${currentOrder.orderCode}\nScanned: ${scanningResult.data}`);
            playErrorSound();
        }
    };

    const handleNextOrder = () => {
        const nextOrderIndex = currentOrderIndex + 1;
        console.log('Next order index:', nextOrderIndex, 'Total orders:', processedOrders.length);

        if (nextOrderIndex < processedOrders.length) {
            setCurrentOrderIndex(nextOrderIndex);
            resetScanState();
        } else {
            // All orders scanned, go back to collection details
            console.log('All orders scanned, going back');
            navigation.goBack();
        }
    };

    const resetScanState = () => {
        setScanned(false);
        setScanSuccess(false);
        setScanError(null);
        setScannedData('');
        setIsScanning(false);
    };

    const startScanning = () => {
        setIsScanning(true);
        resetScanState();
    };

    const retryScanning = () => {
        resetScanState();
        setIsScanning(true);
    };

    // Show error or no orders message
    if (!currentOrder) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">
                    {processedOrders.length === 0 ? 'No orders ready for scanning!' : 'No orders found'}
                </ThemedText>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={{ marginTop: 20, padding: 10, backgroundColor: theme.primary, borderRadius: 8 }}
                >
                    <ThemedText style={{ color: 'white' }}>Back to Collection</ThemedText>
                </TouchableOpacity>
            </ThemedView>
        );
    }

    if (hasPermission === null) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">Requesting camera permission...</ThemedText>
            </ThemedView>
        );
    }

    if (hasPermission === false) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">No access to camera</ThemedText>
                <ThemedText variant="body" style={{ textAlign: 'center', marginTop: 10 }}>
                    Camera permission is required to scan barcodes
                </ThemedText>
            </ThemedView>
        );
    }

    return (
        <ThemedView style={styles.container}>
            {/* Header with return arrow and order info */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                <ThemedText variant="title" style={styles.headerTitle}>
                    Scan Order: {currentOrder.orderNum}
                </ThemedText>
            </View>

            {/* Progress Line */}
            <View style={[styles.progressContainer, { borderBottomColor: theme.text + '50' }]}>
                <View style={styles.progressBackground}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${((currentOrderIndex + 1) / processedOrders.length) * 100}%`,
                                backgroundColor: theme.primary || '#007AFF'
                            }
                        ]}
                    />
                </View>
                <ThemedText variant="body" style={styles.progressText}>
                    Order {currentOrderIndex + 1} of {processedOrders.length}
                </ThemedText>
            </View>

            {/* Scan Content */}
            <Animated.View style={[styles.content, animatedStyle]}>
                {/* Order Details */}
                <View style={[styles.orderDetailsCard, { backgroundColor: theme.cardBackground }]}>
                    <View style={styles.orderInfo}>
                        <ThemedText variant="subtitle" style={styles.orderNumber}>
                            Order: {currentOrder.orderNum}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderCode}>
                            Expected Code: {currentOrder.orderCode}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderStatus}>
                            Status: {currentOrder.status}
                        </ThemedText>
                    </View>
                </View>

                {/* Scanner, Success Card, or Scan Button */}
                <View style={styles.scanContainer}>
                    {isScanning ? (
                        <View style={styles.scannerContainer}>
                            <CameraView
                                style={styles.scanner}
                                facing="back"
                                barcodeScannerSettings={{
                                    barcodeTypes: ["qr", "pdf417", "code128", "code39", "code93", "codabar", "ean13", "ean8", "upc_e", "upc_a"],
                                }}
                                onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
                            />
                            <View style={styles.scanOverlay}>
                                <View style={styles.scanFrame} />
                                <ThemedText variant="body" style={styles.scanInstructions}>
                                    Point camera at barcode
                                </ThemedText>
                            </View>
                        </View>
                    ) : scanSuccess ? (
                        // Success Card with Generated Barcode
                        <View style={[styles.successCard, { backgroundColor: theme.cardBackground }]}>
                            <View style={styles.successHeader}>
                                <AntDesign name="checkcircle" size={32} color="#4CAF50" />
                                <ThemedText variant="title" style={[styles.successTitle, { color: '#4CAF50' }]}>
                                    Scan Successful!
                                </ThemedText>
                            </View>

                            <ThemedText variant="body" style={styles.successMessage}>
                                Order {currentOrder.orderNum} scanned successfully
                            </ThemedText>

                            {/* Generated Barcode */}
                            <View style={styles.barcodeContainer}>
                                <Barcode
                                    value={currentOrder.orderCode}
                                    format="CODE128"
                                    width={2}
                                    height={60}
                                    displayValue={true}
                                    fontSize={14}
                                    textColor="#000"
                                    lineColor="#000"
                                    background="#fff"
                                />
                            </View>

                            <TouchableOpacity
                                style={[styles.continueButton, { backgroundColor: theme.primary }]}
                                onPress={handleNextOrder}
                            >
                                <ThemedText style={[styles.continueButtonText, { color: 'white' }]}>
                                    {currentOrderIndex === processedOrders.length - 1 ? 'Complete' : 'Continue'}
                                </ThemedText>
                            </TouchableOpacity>
                        </View>
                    ) : (
                        <View style={styles.scanButtonContainer}>
                            <TouchableOpacity
                                style={[styles.scanButton, { backgroundColor: theme.primary }]}
                                onPress={startScanning}
                            >
                                <AntDesign name="scan1" size={48} color="white" />
                                <ThemedText style={[styles.scanButtonText, { color: 'white' }]}>
                                    Scan Barcode
                                </ThemedText>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>

                {/* Error Display */}
                {scanError && (
                    <View style={[styles.errorContainer, { backgroundColor: '#ffebee' }]}>
                        <View style={styles.errorHeader}>
                            <AntDesign name="exclamationcircle" size={24} color="#f44336" />
                            <ThemedText variant="subtitle" style={[styles.errorTitle, { color: '#f44336' }]}>
                                Wrong Barcode
                            </ThemedText>
                        </View>
                        <ThemedText variant="body" style={[styles.errorMessage, { color: '#d32f2f' }]}>
                            {scanError}
                        </ThemedText>
                        <TouchableOpacity
                            style={[styles.retryButton, { borderColor: theme.primary }]}
                            onPress={retryScanning}
                        >
                            <ThemedText style={[styles.retryButtonText, { color: theme.primary }]}>
                                Try Again
                            </ThemedText>
                        </TouchableOpacity>
                    </View>
                )}
            </Animated.View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
    },
    backButton: {
        padding: 8,
        marginRight: 16,
    },
    headerTitle: {
        flex: 1,
        textAlign: 'center',
        marginRight: 48, // Compensate for back button width
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    orderDetailsCard: {
        borderRadius: 12,
        padding: 16,
        marginVertical: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    scanContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButtonContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButton: {
        width: 200,
        height: 200,
        borderRadius: 100,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    scanButtonText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 8,
    },
    scannerContainer: {
        flex: 1,
        width: '100%',
        position: 'relative',
    },
    scanner: {
        flex: 1,
    },
    scanOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanFrame: {
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#007AFF',
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    scanInstructions: {
        marginTop: 20,
        textAlign: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
    successCard: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 24,
        borderRadius: 16,
        marginHorizontal: 16,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    successHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    successTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 12,
    },
    successMessage: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 24,
        opacity: 0.8,
    },
    barcodeContainer: {
        backgroundColor: '#fff',
        padding: 16,
        borderRadius: 8,
        marginBottom: 24,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    continueButton: {
        paddingVertical: 12,
        paddingHorizontal: 32,
        borderRadius: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    continueButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    errorContainer: {
        margin: 16,
        padding: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#ffcdd2',
    },
    errorHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    errorTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    errorMessage: {
        fontSize: 14,
        marginBottom: 16,
        lineHeight: 20,
    },
    retryButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 6,
        borderWidth: 1,
        alignSelf: 'flex-start',
    },
    retryButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
});
