import React, { useEffect, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Alert,
    Dimensions,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign } from '@expo/vector-icons';
import { BarCodeScanner } from 'expo-barcode-scanner';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
} from 'react-native-reanimated';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { Order } from '../../navigation/type';
import { CollectionStackParamList } from '../../navigation/type';

type ScanRouteProp = RouteProp<CollectionStackParamList, 'ScanScreen'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'ScanScreen'>;

export default function ScanScreen() {
    const { params } = useRoute<ScanRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const { collectionId, orders } = params;

    // State management
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [scanned, setScanned] = useState(false);
    const [isScanning, setIsScanning] = useState(false);

    // Animation values
    const slideAnimation = useSharedValue(0);

    // Process orders - only show orders with all products gathered
    const processedOrders = orders
        .filter(order => order.ordersProducts.every(product => product.status === 'gathered'))
        .sort((a, b) => a.orderNum.localeCompare(b.orderNum));

    const currentOrder = processedOrders[currentOrderIndex];

    // Request camera permissions
    useEffect(() => {
        const getBarCodeScannerPermissions = async () => {
            const { status } = await BarCodeScanner.requestPermissionsAsync();
            setHasPermission(status === 'granted');
        };

        getBarCodeScannerPermissions();
    }, []);

    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);

    // Animated style for slide transitions
    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: slideAnimation.value * 300 }],
        };
    });

    const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
        setScanned(true);
        setIsScanning(false);

        console.log('Scanned barcode:', data);
        console.log('Expected orderCode:', currentOrder.orderCode);

        if (data === currentOrder.orderCode) {
            // Success - move to next order
            Alert.alert(
                'Success!',
                `Order ${currentOrder.orderNum} scanned successfully!`,
                [
                    {
                        text: 'Continue',
                        onPress: handleNextOrder
                    }
                ]
            );
        } else {
            // Failed - wrong barcode
            Alert.alert(
                'Wrong Barcode',
                `Expected: ${currentOrder.orderCode}\nScanned: ${data}`,
                [
                    {
                        text: 'Try Again',
                        onPress: () => setScanned(false)
                    }
                ]
            );
        }
    };

    const handleNextOrder = () => {
        const nextOrderIndex = currentOrderIndex + 1;
        console.log('Next order index:', nextOrderIndex, 'Total orders:', processedOrders.length);

        if (nextOrderIndex < processedOrders.length) {
            setCurrentOrderIndex(nextOrderIndex);
            setScanned(false);
        } else {
            // All orders scanned, go back to collection details
            console.log('All orders scanned, going back');
            Alert.alert(
                'Scanning Complete!',
                'All orders have been scanned successfully.',
                [
                    {
                        text: 'Back to Collection',
                        onPress: () => navigation.goBack()
                    }
                ]
            );
        }
    };

    const startScanning = () => {
        setIsScanning(true);
        setScanned(false);
    };

    // Show error or no orders message
    if (!currentOrder) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">
                    {processedOrders.length === 0 ? 'No orders ready for scanning!' : 'No orders found'}
                </ThemedText>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={{ marginTop: 20, padding: 10, backgroundColor: theme.primary, borderRadius: 8 }}
                >
                    <ThemedText style={{ color: 'white' }}>Back to Collection</ThemedText>
                </TouchableOpacity>
            </ThemedView>
        );
    }

    if (hasPermission === null) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">Requesting camera permission...</ThemedText>
            </ThemedView>
        );
    }

    if (hasPermission === false) {
        return (
            <ThemedView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ThemedText variant="title">No access to camera</ThemedText>
                <ThemedText variant="body" style={{ textAlign: 'center', marginTop: 10 }}>
                    Camera permission is required to scan barcodes
                </ThemedText>
            </ThemedView>
        );
    }

    return (
        <ThemedView style={styles.container}>
            {/* Header with return arrow and order info */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                <ThemedText variant="title" style={styles.headerTitle}>
                    Scan Order: {currentOrder.orderNum}
                </ThemedText>
            </View>

            {/* Progress Line */}
            <View style={[styles.progressContainer, { borderBottomColor: theme.text + '50' }]}>
                <View style={styles.progressBackground}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${((currentOrderIndex + 1) / processedOrders.length) * 100}%`,
                                backgroundColor: theme.primary || '#007AFF'
                            }
                        ]}
                    />
                </View>
                <ThemedText variant="body" style={styles.progressText}>
                    Order {currentOrderIndex + 1} of {processedOrders.length}
                </ThemedText>
            </View>

            {/* Scan Content */}
            <Animated.View style={[styles.content, animatedStyle]}>
                {/* Order Details */}
                <View style={[styles.orderDetailsCard, { backgroundColor: theme.cardBackground }]}>
                    <View style={styles.orderInfo}>
                        <ThemedText variant="subtitle" style={styles.orderNumber}>
                            Order: {currentOrder.orderNum}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderCode}>
                            Expected Code: {currentOrder.orderCode}
                        </ThemedText>
                        <ThemedText variant="body" style={styles.orderStatus}>
                            Status: {currentOrder.status}
                        </ThemedText>
                    </View>
                </View>

                {/* Scanner or Scan Button */}
                <View style={styles.scanContainer}>
                    {isScanning ? (
                        <View style={styles.scannerContainer}>
                            <BarCodeScanner
                                onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
                                style={styles.scanner}
                            />
                            <View style={styles.scanOverlay}>
                                <View style={styles.scanFrame} />
                                <ThemedText variant="body" style={styles.scanInstructions}>
                                    Point camera at barcode
                                </ThemedText>
                            </View>
                        </View>
                    ) : (
                        <View style={styles.scanButtonContainer}>
                            <TouchableOpacity
                                style={[styles.scanButton, { backgroundColor: theme.primary }]}
                                onPress={startScanning}
                            >
                                <AntDesign name="scan1" size={48} color="white" />
                                <ThemedText style={[styles.scanButtonText, { color: 'white' }]}>
                                    Scan Barcode
                                </ThemedText>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </Animated.View>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 4,
        borderBottomWidth: 1,
    },
    backButton: {
        padding: 8,
        marginRight: 16,
    },
    headerTitle: {
        flex: 1,
        textAlign: 'center',
        marginRight: 48, // Compensate for back button width
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    orderDetailsCard: {
        borderRadius: 12,
        padding: 16,
        marginVertical: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    scanContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButtonContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanButton: {
        width: 200,
        height: 200,
        borderRadius: 100,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    scanButtonText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 8,
    },
    scannerContainer: {
        flex: 1,
        width: '100%',
        position: 'relative',
    },
    scanner: {
        flex: 1,
    },
    scanOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanFrame: {
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#007AFF',
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    scanInstructions: {
        marginTop: 20,
        textAlign: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
});
