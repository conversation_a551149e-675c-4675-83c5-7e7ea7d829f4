import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import useTheme from '../theme/useTheme';

interface ThemedIconProps {
    name: keyof typeof Ionicons.glyphMap;
    size?: number;
    color?: string;
}

export default function ThemedIcon({ name, size = 24, color }: ThemedIconProps) {
    const theme = useTheme();

    return <Ionicons name={name} size={size} color={color ?? theme.text} />;
}
