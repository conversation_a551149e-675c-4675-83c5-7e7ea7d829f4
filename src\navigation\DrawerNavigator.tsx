import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import DashboardScreen from '../screens/DashboardScreen';
import CollectionsScreen from '../screens/CollectionsScreen';
import ReturnsScreen from '../screens/ReturnsScreen';
import CustomDrawerContent from '../components/CustomDrawerContent';
import { DrawerParamList } from './type';
import useTheme from '../theme/useTheme';

const Drawer = createDrawerNavigator<DrawerParamList>();

export default function DrawerNavigator() {
    const theme = useTheme();

    return (
        <Drawer.Navigator
            initialRouteName="Collections"
            drawerContent={(props) => <CustomDrawerContent {...props} />}
            screenOptions={{
                headerShown: false,
                drawerStyle: {
                    backgroundColor: theme.background,
                    width: 280,
                },
                drawerType: 'front',
                overlayColor: 'rgba(0, 0, 0, 0.5)',
            }}
        >
            <Drawer.Screen
                name="Dashboard"
                component={DashboardScreen}
                options={{
                    drawerLabel: 'Dashboard',
                }}
            />
            <Drawer.Screen
                name="Collections"
                component={CollectionsScreen}
                options={{
                    drawerLabel: 'Collections',
                }}
            />
            <Drawer.Screen
                name="Returns"
                component={ReturnsScreen}
                options={{
                    drawerLabel: 'Returns',
                }}
            />
        </Drawer.Navigator>
    );
}
