// src/features/collections/collectionSlice.ts
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { fetchCollectionById, fetchCollectionsByWarehouseId } from './CollectionsThunks';
import axiosClient from '../../api/axiosClient';
import { Collection } from '../../navigation/type';


const initialState: CollectionsState = {
    collections: [],
    loading: false,
    error: null,
};

interface CollectionsState {
    collections: Collection[];
    loading: boolean;
    error: string | null;
}




const collectionSlice = createSlice({
    name: 'collections',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchCollectionsByWarehouseId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchCollectionsByWarehouseId.fulfilled, (state, action) => {
                state.loading = false;
                state.collections = action.payload;
            })
            .addCase(fetchCollectionsByWarehouseId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(fetchCollectionById.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(fetchCollectionById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(fetchCollectionById.pending, (state) => {
                state.loading = true;
                state.error = null;
            });
    },
});

export default collectionSlice.reducer;
