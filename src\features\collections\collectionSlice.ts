// src/features/collections/collectionSlice.ts
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { fetchCollectionsByWarehouseId } from './CollectionsThunks';
import axiosClient from '../../api/axiosClient';

export interface Collection {
    id: number;
    name: string;
    location: string;
    status: string;
}

const initialState: CollectionsState = {
    collections: [],
    loading: false,
    error: null,
};

interface CollectionsState {
    collections: Collection[];
    loading: boolean;
    error: string | null;
}




const collectionSlice = createSlice({
    name: 'collections',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchCollectionsByWarehouseId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchCollectionsByWarehouseId.fulfilled, (state, action) => {
                state.loading = false;
                state.collections = action.payload;
            })
            .addCase(fetchCollectionsByWarehouseId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            });
    },
});

export default collectionSlice.reducer;
