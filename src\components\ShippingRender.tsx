import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import useTheme from '../theme/useTheme';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';

export type StatusType = 'created' | 'processing' | 'shipped';

interface Props {
    status: string;
}

export default function ShippingRender({ status }: Props) {
    const theme = useTheme();

    const getStatusStyle = () => {
        switch (status) {
            case 'created':
                return { backgroundColor: '#FFD70030', borderColor: "#FFD700", label: 'Created' }; // gold
            case 'delivery':
                return { backgroundColor: '#1E90FF30', borderColor: "#1E90FF", label: 'Delivery man' }; // blue
            case 'shipping':
                return { backgroundColor: '#32CD3230', borderColor: "#32CD32", label: 'Shipping Company' }; // green
            default:
                return { backgroundColor: '#ccc30', borderColor: "#ccc", label: 'Unknown' };
        }
    };

    const { backgroundColor, borderColor, label } = getStatusStyle();

    return (
        <View style={styles.chip}>
            {status === 'delivery' ? <MaterialIcons size={24} name="delivery-dining" color={borderColor} />
                : <FontAwesome5 name="truck" color={borderColor} />}
            <Text style={[styles.text, { color: borderColor }]}>
                {label}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    chip: {
        borderRadius: 20,
        display: 'flex',
        flexDirection: 'row',
        gap: 6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    text: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});
