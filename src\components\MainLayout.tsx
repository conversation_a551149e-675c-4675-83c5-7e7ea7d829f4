import React, { useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform, StatusBar as RNStatusBar, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import WarehouseDropdown from './WarehouseDropdown';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { useThemeMode } from '../theme/ThemeContext';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';
import { clearSelectedWarehouse, fetchWarehouses } from '../features/warehouses/warehouseSlice';

type TabType = 'collections' | 'returns';

interface MainLayoutProps {
    children: React.ReactNode;
    activeTab: TabType;
    onTabChange: (tab: TabType) => void;
}

export default function MainLayout({ children, activeTab, onTabChange }: MainLayoutProps) {
    const theme = useTheme();
    const { resolvedTheme } = useThemeMode();
    const dispatch = useDispatch<AppDispatch>();
    const { collections, loading } = useSelector((state: RootState) => state.collections);


    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
            <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} />

            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.background, position: 'relative', borderBottomColor: theme.text + '20' }]}>
                {/* <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                    <MaterialIcons name="logout" size={24} color="red" />
                </TouchableOpacity> */}

                <WarehouseDropdown
                    onWarehouseSelect={() => {
                        // Switch to collections tab and navigate to collection list
                        onTabChange('collections');
                    }}
                />
            </View>

            {/* Tabs */}
            <View style={[styles.tabContainer, { backgroundColor: theme.background, borderBottomColor: theme.text + '20' }]}>
                <TouchableOpacity
                    style={[
                        styles.tab,
                        activeTab === 'collections' && { borderBottomColor: theme.primary, borderBottomWidth: 2 }
                    ]}
                    onPress={() => onTabChange('collections')}
                >
                    <ThemedText style={[
                        styles.tabText,
                        { color: activeTab === 'collections' ? theme.primary : theme.text + '80' }
                    ]}>
                        Collections

                    </ThemedText>
                    {/* collection number chip */}
                    <View style={{ backgroundColor: theme.primary, borderRadius: 999, minHeight: 20, minWidth: 20, display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>

                        {loading ? <ActivityIndicator size="small" color={theme.background} /> : <ThemedText variant="body" style={{ color: theme.background }}>{collections.length}</ThemedText>}
                    </View>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[
                        styles.tab,
                        activeTab === 'returns' && { borderBottomColor: theme.primary, borderBottomWidth: 2 }
                    ]}
                    onPress={() => onTabChange('returns')}
                >
                    <ThemedText style={[
                        styles.tabText,
                        { color: activeTab === 'returns' ? theme.primary : theme.text + '80' }
                    ]}>
                        Returns
                    </ThemedText>
                </TouchableOpacity>
            </View>

            {/* Content */}
            <View style={[styles.content, { backgroundColor: theme.background }]}>
                {children}
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        // paddingTop: Platform.OS === 'android' ? RNStatusBar.currentHeight || 0 : 0,
    },
    header: {
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottomWidth: 1,

    },
    logoutButton: {
        padding: 5,
    },
    tabContainer: {
        flexDirection: 'row',
        height: 48,
        borderBottomWidth: 1,
    },
    tab: {
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        gap: 6,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
    },
    tabText: {
        fontSize: 16,
        fontWeight: '600',
    },
    content: {
        flex: 1,
    },
});
