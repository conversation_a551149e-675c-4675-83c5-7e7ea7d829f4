export const fakeOrders = [
    {
        id: 1,
        orderNum: 'ORD1001',
        orderCode: '5dc20a5b',
        status: 'created',
        goodsDescription: '3 x Charger USB-C<br>1 x Phone Case',
        ordersProducts: [
            { name: 'Charger USB-C', quantity: 3, barcode: '8d7c8c9a29' },
            { name: 'Phone Case', quantity: 1, barcode: 'ac923bca31' },
        ],
        sku: 'PROD004',
    },
    {
        id: 2,
        orderNum: 'ORD1002',
        orderCode: '584172d3',
        status: 'created',
        goodsDescription: '2 x Charger USB-C<br>2 x Wireless Earbuds <br>1 x Power Bank<br>3 x Screen Protector',
        ordersProducts: [
            { name: 'Charger USB-C', quantity: 2, barcode: 'b4cb6b8dd2' },
            { name: 'Wireless Earbuds', quantity: 2, barcode: 'd3ed7eec54' },
            { name: 'Power Bank', quantity: 1, barcode: 'f5e4d3c2b1' },
            { name: 'Screen Protector', quantity: 3, barcode: 'a1b2c3d4e5' },
        ],
        sku: 'PROD004',
    },
    {
        id: 12,
        orderNum: 'ORD1012',
        orderCode: 'pqr9j234',
        status: 'created',
        goodsDescription: '2 x Power Bank',
        ordersProducts: [
            { name: 'Screen Protector', quantity: 2, barcode: 'r7s8t9u0v1' },
        ],
        sku: 'PROD004',
    },
    {
        id: 11,
        orderNum: 'ORD1011',
        orderCode: 'pqr9j234',
        status: 'created',
        goodsDescription: '2 x Power Bank<br>1 x Charger USB-C<br>2 x Screen Protector',
        ordersProducts: [
            { name: 'Power Bank', quantity: 2, barcode: 'h7i8j9k0l1' },
            { name: 'Charger USB-C', quantity: 1, barcode: 'm2n3o4p5q6' },
            { name: 'Screen Protector', quantity: 2, barcode: 'r7s8t9u0v1' },
        ],
        sku: 'PROD004',
    },

    {
        id: 3,
        orderNum: 'ORD1003',
        orderCode: '5d71bc1e',
        status: 'shipped',
        goodsDescription: '1 x Phone Case<br>2 x Bluetooth Speaker',
        ordersProducts: [
            { name: 'Phone Case', quantity: 1, barcode: '7e9ff09c61' },
            { name: 'Bluetooth Speaker', quantity: 2, barcode: 'bbccddee11' },
        ],
        sku: 'PROD003',
    },
    {
        id: 4,
        orderNum: 'ORD1004',
        orderCode: 'ec146eac',
        status: 'shipped',
        goodsDescription: '2 x Shirt M<br>1 x Hat',
        ordersProducts: [
            { name: 'Shirt M', quantity: 2, barcode: '7df11c7234' },
            { name: 'Hat', quantity: 1, barcode: 'eab112dd34' },
        ],
        sku: 'PROD002',
    },
    {
        id: 5,
        orderNum: 'ORD1005',
        orderCode: 'ddc4d478',
        status: 'created',
        goodsDescription: '1 x Shirt M<br>1 x Watch',
        ordersProducts: [
            { name: 'Shirt M', quantity: 1, barcode: '4d547b6bb5' },
            { name: 'Watch', quantity: 1, barcode: 'bcde788a12' },
        ],
        sku: 'PROD002',
    },
    {
        id: 6,
        orderNum: 'ORD1006',
        orderCode: 'abc4e789',
        status: 'created',
        goodsDescription: '1 x Wireless Earbuds<br>1 x Laptop Bag',
        ordersProducts: [
            { name: 'Wireless Earbuds', quantity: 1, barcode: '5e658c7dd6' },
            { name: 'Laptop Bag', quantity: 1, barcode: '99887ffee1' },
        ],
        sku: 'PROD001',
    },
    {
        id: 7,
        orderNum: 'ORD1007',
        orderCode: 'def5f890',
        status: 'shipped',
        goodsDescription: '2 x Power Bank<br>1 x Charger USB-C',
        ordersProducts: [
            { name: 'Power Bank', quantity: 2, barcode: '6f769d8ee7' },
            { name: 'Charger USB-C', quantity: 1, barcode: 'aabbcc9988' },
        ],
        sku: 'PROD005',
    },
    {
        id: 8,
        orderNum: 'ORD1008',
        orderCode: 'ghi6g901',
        status: 'created',
        goodsDescription: '1 x Bluetooth Speaker<br>1 x Phone Case',
        ordersProducts: [
            { name: 'Bluetooth Speaker', quantity: 1, barcode: '7g870e9ff8' },
            { name: 'Phone Case', quantity: 1, barcode: 'ccddeeff55' },
        ],
        sku: 'PROD006',
    },
    {
        id: 9,
        orderNum: 'ORD1009',
        orderCode: 'jkl7h012',
        status: 'shipped',
        goodsDescription: '1 x Smart Watch<br>1 x Earbuds',
        ordersProducts: [
            { name: 'Smart Watch', quantity: 1, barcode: '8h981f0gg9' },
            { name: 'Earbuds', quantity: 1, barcode: 'aa11bb22cc' },
        ],
        sku: 'PROD007',
    },
    {
        id: 10,
        orderNum: 'ORD1010',
        orderCode: 'mno8i123',
        status: 'created',
        goodsDescription: '1 x Laptop Bag<br>1 x Mouse Pad',
        ordersProducts: [
            { name: 'Laptop Bag', quantity: 1, barcode: '9i092g1hh0' },
            { name: 'Mouse Pad', quantity: 1, barcode: 'ff00998877' },
        ],
        sku: 'PROD008',
    },
];

export const extractedProducts = fakeOrders.flatMap(order =>
    order.ordersProducts.map(product => ({
        orderId: order.id,
        name: product.name,
        quantity: product.quantity,
        status: order.status,
        sku: order.sku,
        returnImg: null, // or default placeholder URL
        isChecked: false,
    }))
);
