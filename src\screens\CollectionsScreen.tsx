import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useNavigation, useNavigationState } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import GlobalLayout from '../components/GlobalLayout';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import ScanScreen from '../features/collections/ScanScreen';
import { CollectionStackParamList } from '../navigation/type';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

const Stack = createNativeStackNavigator<CollectionStackParamList>();

export default function CollectionsScreen() {



    return (
        <GlobalLayout>
            <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="CollectionList">
                <Stack.Screen name="CollectionList" component={CollectionListScreen} />
                <Stack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
                <Stack.Screen name="Fulfillment" component={FulfillmentScreen} />
                <Stack.Screen name="ScanScreen" component={ScanScreen} />
            </Stack.Navigator>
        </GlobalLayout>
    );
}
