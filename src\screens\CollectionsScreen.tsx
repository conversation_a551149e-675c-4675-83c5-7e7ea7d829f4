import React, { useEffect, useRef } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainerRef } from '@react-navigation/native';
import CollectionsLayout from '../components/CollectionsLayout';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import { CollectionStackParamList } from '../navigation/type';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';

const Stack = createNativeStackNavigator<CollectionStackParamList>();

export default function CollectionsScreen() {
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const navigationRef = useRef<NavigationContainerRef<CollectionStackParamList>>(null);
    const [previousWarehouseId, setPreviousWarehouseId] = React.useState<number | null>(null);

    // Reset navigation to CollectionList when warehouse changes
    useEffect(() => {
        if (selectedWarehouse && selectedWarehouse.id !== previousWarehouseId) {
            console.log('Warehouse changed, resetting navigation to CollectionList');

            // Reset navigation to collection list
            if (navigationRef.current) {
                navigationRef.current.reset({
                    index: 0,
                    routes: [{ name: 'CollectionList' }],
                });
            }

            setPreviousWarehouseId(selectedWarehouse.id);
        }
    }, [selectedWarehouse, previousWarehouseId]);

    return (
        <CollectionsLayout>
            <Stack.Navigator
                screenOptions={{ headerShown: false }}
                initialRouteName="CollectionList"
            >
                <Stack.Screen name="CollectionList" component={CollectionListScreen} />
                <Stack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
                <Stack.Screen name="Fulfillment" component={FulfillmentScreen} />
            </Stack.Navigator>
        </CollectionsLayout>
    );
}
