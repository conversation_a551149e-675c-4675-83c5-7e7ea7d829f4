import React from 'react';
import { TouchableOpacity, Text, StyleSheet, GestureResponderEvent } from 'react-native';
import useTheme from '../theme/useTheme';

type Props = {
    title: string;
    disabled?: boolean;
    onPress: (event: GestureResponderEvent) => void;


};

export default function ThemedButton({ title, disabled, onPress }: Props) {
    const theme = useTheme();

    return (
        <TouchableOpacity
            disabled={disabled}
            onPress={onPress}
            style={[styles.button, { backgroundColor: theme.button }]}
        >
            <Text style={[styles.text, { color: '#fff' }]}>{title}</Text>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        alignItems: 'center',
    },
    text: {
        fontWeight: 'bold',
        fontSize: 16,
    },
});
