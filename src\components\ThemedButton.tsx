import React from 'react';
import { TouchableOpacity, Text, StyleSheet, GestureResponderEvent } from 'react-native';
import useTheme from '../theme/useTheme';
import ThreeDotsSkeleton from './ThreeDotsSkeleton';

type Props = {
    title: string;
    disabled?: boolean;
    onPress: (event: GestureResponderEvent) => void;
    loading?: boolean;

};

export default function ThemedButton({ title, disabled, onPress, loading }: Props) {
    const theme = useTheme();

    return (
        <TouchableOpacity
            disabled={disabled}
            onPress={onPress}
            style={[styles.button, { backgroundColor: theme.button }]}
        >
            {loading ? <ThreeDotsSkeleton /> : <Text style={[styles.text, { color: '#fff' }]}>{title}</Text>}
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 50
    },
    text: {
        fontWeight: 'bold',
        fontSize: 16,
    },
});
