import React, { useState } from 'react';
import { View, TouchableOpacity, FlatList, Dimensions, Pressable, StyleSheet } from 'react-native';
import { AntDesign, FontAwesome6 } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import ThemedText from './ThemedText';
import { Warehouse } from '../features/warehouses/warehouseSlice';
import useTheme from '../theme/useTheme';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../app/store';
import { selectWarehouse } from '../features/warehouses/warehouseSlice';
import { fetchCollectionsByWarehouseId } from '../features/collections/CollectionsThunks';
import ThreeDotsSkeleton from './ThreeDotsSkeleton';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

interface WarehouseDropdownProps {
    onWarehouseSelect?: (warehouse: Warehouse) => void;
}

export default function WarehouseDropdown({ onWarehouseSelect }: WarehouseDropdownProps) {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { warehouses, selectedWarehouse, loading } = useSelector((state: RootState) => state.warehouses);
    const [isOpen, setIsOpen] = useState(false);
    const dropdownHeight = useSharedValue(0);

    const toggleDropdown = () => {
        if (isOpen) {
            dropdownHeight.value = withTiming(0, { duration: 200 });
            setTimeout(() => setIsOpen(false), 200);
        } else {
            setIsOpen(true);
            dropdownHeight.value = withTiming(screenHeight * 0.3, { duration: 200 });
        }
    };

    const dropdownAnimatedStyle = useAnimatedStyle(() => ({
        height: dropdownHeight.value,
        overflow: 'hidden',
    }));

    const handleWarehouseSelect = (warehouse: Warehouse) => {
        setIsOpen(false);
        dropdownHeight.value = withTiming(0, { duration: 200 });
        dispatch(selectWarehouse(warehouse));
        dispatch(fetchCollectionsByWarehouseId(warehouse.id));
        onWarehouseSelect?.(warehouse);
    };

    const renderWarehouseItem = ({ item }: { item: Warehouse }) => {
        const isSelected = selectedWarehouse?.id === item.id;

        return (
            <TouchableOpacity
                style={[styles.dropdownItem, {
                    backgroundColor: isSelected ? theme.primary + '20' : theme.cardBackground,
                    borderBottomColor: theme.text + '10'
                }]}
                onPress={() => handleWarehouseSelect(item)}
            >
                <View style={styles.iconRow}>
                    <FontAwesome6 name="cubes-stacked" size={20} color={theme.text} />
                    <ThemedText variant="body">{item.collections}</ThemedText>
                </View>
                <View style={styles.nameColumn}>
                    <ThemedText style={[styles.warehouseName, { color: isSelected ? theme.primary : theme.text }]}> {item.name} </ThemedText>
                    <ThemedText style={[styles.warehouseLocation, { color: isSelected ? theme.primary : theme.text + '80' }]}> {item.location} </ThemedText>
                </View>
                {isSelected && <AntDesign name="check" size={16} color={theme.primary} style={styles.checkIcon} />}
            </TouchableOpacity>
        );
    };

    return (
        <View style={styles.container}>
            <TouchableOpacity style={[styles.header, { backgroundColor: theme.background }]} onPress={toggleDropdown}>
                <ThemedText style={styles.selectedWarehouse} numberOfLines={1}>
                    {selectedWarehouse?.name || 'Select Warehouse'}
                </ThemedText>
                <AntDesign name={isOpen ? 'up' : 'down'} size={16} color={theme.text} style={styles.arrow} />
            </TouchableOpacity>

            {isOpen && (
                <View style={styles.fullscreenWrapper}>
                    <Pressable style={styles.fullscreenOverlay} onPress={toggleDropdown} />

                    <Animated.View style={[styles.dropdown, { backgroundColor: theme.cardBackground }, dropdownAnimatedStyle]}>
                        {loading ? (
                            <View style={styles.loadingContainer}>
                                <ThreeDotsSkeleton />
                            </View>
                        ) : (
                            <FlatList
                                data={warehouses}
                                keyExtractor={(item) => item.id.toString()}
                                renderItem={renderWarehouseItem}
                                showsVerticalScrollIndicator={false}
                                style={styles.list}
                            />
                        )}
                    </Animated.View>
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 12,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
    },
    selectedWarehouse: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
        marginRight: 8,
    },
    arrow: {
        marginLeft: 4,
    },
    fullscreenWrapper: {
        position: 'absolute',
        top: 40,
        width: screenWidth,
        height: screenHeight,
        zIndex: 999,
        paddingHorizontal: 12,
    },
    fullscreenOverlay: {
        position: 'absolute',
        top: 0,
        width: screenWidth,
        height: screenHeight,
        backgroundColor: 'rgba(0,0,0,0.6)',
        zIndex: 1,
    },
    dropdown: {
        position: 'absolute',
        top: 5,
        minWidth: 300,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        zIndex: 2,
        elevation: 6,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        left: 12,
    },
    list: {
        maxHeight: screenHeight * 0.4,
    },
    dropdownItem: {
        padding: 12,
        borderBottomWidth: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        marginRight: 12,
    },
    nameColumn: {
        flex: 1,
        flexDirection: 'column',
        gap: 2,
        justifyContent: 'center',
        alignItems: 'flex-start',
    },
    warehouseName: {
        fontSize: 14,
        fontWeight: '600',
        flex: 1,
    },
    warehouseLocation: {
        fontSize: 12,
        marginRight: 8,
    },
    checkIcon: {
        marginLeft: 8,
    },
    loadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
});
