import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, FlatList, Dimensions, TouchableWithoutFeedback } from 'react-native';
import { AntDesign, FontAwesome6 } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import { Warehouse } from '../features/warehouses/warehouseSlice';
import useTheme from '../theme/useTheme';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../app/store';
import { selectWarehouse, fetchWarehouses } from '../features/warehouses/warehouseSlice';
import { fetchCollectionsByWarehouseId } from '../features/collections/CollectionsThunks';
import ThreeDotsSkeleton from './ThreeDotsSkeleton';

const { height: screenHeight } = Dimensions.get('window');

interface WarehouseDropdownProps {
    onWarehouseSelect?: (warehouse: Warehouse) => void;
}

export default function WarehouseDropdown({ onWarehouseSelect }: WarehouseDropdownProps) {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { warehouses, selectedWarehouse, loading } = useSelector((state: RootState) => state.warehouses);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        // Load warehouses when component mounts
        dispatch(fetchWarehouses());
    }, [dispatch]);

    const handleWarehouseSelect = (warehouse: Warehouse) => {
        console.log('Warehouse selected:', warehouse.name);

        // Close dropdown first
        setIsOpen(false);

        // Select warehouse in store
        dispatch(selectWarehouse(warehouse));

        // Fetch collections for the new warehouse
        dispatch(fetchCollectionsByWarehouseId(warehouse.id));

        // Call callback if provided
        onWarehouseSelect?.(warehouse);
    };

    const renderWarehouseItem = ({ item }: { item: Warehouse }) => {
        const isSelected = selectedWarehouse?.id === item.id;

        return (
            <TouchableOpacity
                style={[
                    styles.dropdownItem,
                    {
                        backgroundColor: isSelected ? theme.primary + '20' : theme.cardBackground,
                        borderBottomColor: theme.text + '10',
                    }
                ]}
                onPress={() => handleWarehouseSelect(item)}
            >
                {/* add collection icons */}
                < View style={{ display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center', marginRight: 12 }}>
                    <FontAwesome6 name="cubes-stacked" size={20} color={theme.text} />
                    <ThemedText variant="body">{item.collections}</ThemedText>
                </ View>
                {/* warehouse name and location */}
                <View style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2, justifyContent: 'center', alignItems: 'flex-start' }}>
                    <ThemedText style={[
                        styles.warehouseName,
                        { color: isSelected ? theme.primary : theme.text }
                    ]}>
                        {item.name}
                    </ThemedText>
                    <ThemedText style={[
                        styles.warehouseLocation,
                        { color: isSelected ? theme.primary : theme.text + '80' }
                    ]}>
                        {item.location}
                    </ThemedText>
                </View>

                {isSelected && (
                    <AntDesign name="check" size={16} color={theme.primary} style={styles.checkIcon} />
                )}
            </TouchableOpacity>
        );
    };


    return (
        <View style={styles.container}>
            <TouchableOpacity
                style={[styles.header, { backgroundColor: theme.background }]}
                onPress={() => setIsOpen(!isOpen)}
            >
                <ThemedText style={styles.selectedWarehouse} numberOfLines={1}>
                    {selectedWarehouse?.name || 'Select Warehouse'}
                </ThemedText>
                <AntDesign
                    name={isOpen ? "up" : "down"}
                    size={16}
                    color={theme.text}
                    style={styles.arrow}
                />
            </TouchableOpacity>

            {isOpen && (
                <View style={[
                    styles.dropdown,
                    {
                        backgroundColor: theme.cardBackground,
                        borderColor: theme.text + '20',
                        maxHeight: screenHeight * 0.4,
                    }
                ]}>
                    {loading ? (
                        <View style={styles.loadingContainer}>
                            <ThreeDotsSkeleton />
                        </View>
                    ) : (
                        <FlatList
                            data={warehouses}
                            keyExtractor={(item) => item.id.toString()}
                            renderItem={renderWarehouseItem}
                            showsVerticalScrollIndicator={false}
                            style={styles.list}
                        />
                    )}
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 999,
    },
    container: {
        position: 'relative',
        flex: 1,
        maxWidth: 200,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
    },
    selectedWarehouse: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
        marginRight: 8,
    },
    arrow: {
        marginLeft: 4,
    },
    dropdown: {
        position: 'absolute',
        top: '100%',
        right: 0,
        borderWidth: 1,
        borderRadius: 8,
        overflow: 'hidden',
        marginTop: 4,
        zIndex: 1000,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        minWidth: 300,
    },
    list: {
        maxHeight: 200,
    },
    dropdownItem: {
        padding: 12,
        borderBottomWidth: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    warehouseName: {
        fontSize: 14,
        fontWeight: '600',
        flex: 1,
    },
    warehouseLocation: {
        fontSize: 12,
        marginRight: 8,
    },
    checkIcon: {
        marginLeft: 8,
    },
    loadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
});
