import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import useTheme from '../theme/useTheme';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withRepeat,
    withTiming,
    Easing,
} from 'react-native-reanimated';

export default function PageTitleSkeleton() {
    const theme = useTheme();

    const opacity = useSharedValue(0.2);

    useEffect(() => {
        opacity.value = withRepeat(
            withTiming(0.2, { duration: 800, easing: Easing.inOut(Easing.ease) }),
            -1,
            true
        );
    }, []);

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));

    return (
        <Animated.View style={[styles.container, animatedStyle]}>
            <View style={[styles.box, { width: 30, backgroundColor: theme.text }]} />
            <View style={[styles.box, { width: 100, backgroundColor: theme.text }]} />
        </Animated.View>
    );
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        gap: 8,
        borderRadius: 8,
        alignItems: 'center',
    },
    box: {
        height: 40,
        borderRadius: 8,
    },
});
