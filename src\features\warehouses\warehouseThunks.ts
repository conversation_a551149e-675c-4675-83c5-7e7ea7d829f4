import { createAsyncThunk } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { ENDPOINTS } from '../../api/endPoints';

export const fetchWarehouses = createAsyncThunk(
    'warehouses/fetch',
    async (_, thunkAPI) => {
        try {
            const response = await axiosClient.get(ENDPOINTS.WAREHOUSES);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Error');
        }
    }
);
