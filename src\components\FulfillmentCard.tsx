// components/FulfillmentCard.tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import ThemedText from './ThemedText';
import { Ionicons } from '@expo/vector-icons';

export default function FulfillmentCard({ sku, tasks, completedCount, items, onToggle }: any) {
    return (
        <View style={styles.card}>
            <ThemedText style={styles.title}>SKU: {sku}</ThemedText>
            <ThemedText style={styles.subtext}>{completedCount} of {tasks} Tasks</ThemedText>
            {items.map((item: any, idx: number) => (
                <TouchableOpacity
                    key={idx}
                    style={styles.itemRow}
                    onPress={() => onToggle(item.barcode)}
                >
                    <Ionicons
                        name={item.checked ? 'checkbox' : 'square-outline'}
                        size={22}
                        color="green"
                    />
                    <ThemedText style={[styles.itemText, item.checked && styles.checked]}>
                        {item.name} — Qty: {item.quantity}
                    </ThemedText>
                </TouchableOpacity>
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    card: {
        backgroundColor: '#a3e4a1',
        padding: 16,
        marginVertical: 8,
        marginHorizontal: 12,
        borderRadius: 16,
        elevation: 4,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    subtext: {
        fontSize: 14,
        marginBottom: 12,
    },
    itemRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    itemText: {
        marginLeft: 10,
        fontSize: 16,
    },
    checked: {
        textDecorationLine: 'line-through',
        color: '#555',
    },
});
